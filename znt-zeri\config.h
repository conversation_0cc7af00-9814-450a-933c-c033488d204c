#pragma once
#include "sdk.h"

namespace zeri {
    struct Config {
        MENU_PAGE(MAIN_PAGE, "kurisu:zeri");
        MENU_TOGGLE(ENABLED, "kurisu:zeri:enabled", enabled, true);

        MENU_PAGE(COMBO_PAGE, "kurisu:zeri:combo");
        MENU_TOGGLE(COMBO_USE_Q, "kurisu:zeri:combo:use:q", use_q, true);
        MENU_TOGGLE(COMBO_USE_Q_ANCHOR, "kurisu:zeri:combo:use:q:anchor", use_q_anchor, true);
        MENU_TOGGLE(KILLSTEAL_ANCHOR_Q, "kurisu:zeri:combo:killsteal:anchor:q", killsteal_anchor_q, true);
        MENU_TOGGLE(COMBO_USE_W, "kurisu:zeri:combo:use:w", use_w, true);
        MENU_TOGGLE(COMBO_USE_E, "kurisu:zeri:combo:use:e", use_e, true);
        MENU_SLIDER_I32(COMBO_MIN_SAFE_DISTANCE, "kurisu:zeri:combo:min_safe_distance", min_combo_safe_distance, 250);
        MENU_TOGGLE(COMBO_DIVE_TURRET, "kurisu:zeri:combo:use:e:dive_turret", combo_use_e_dive_turret, true);
        MENU_TOGGLE(COMBO_USE_R, "kurisu:zeri:combo:use:r", use_r, true);
        MENU_TOGGLE(BLOCK_MANUAL_R, "kurisu:zeri:combo:block_manual_r", block_manual_r, true);

        MENU_PAGE(HARASS_PAGE, "kurisu:zeri:harass");
        MENU_SLIDER_I32(HARASS_MIN_MANA_PERCENT, "kurisu:zeri:harass:min:mana:percent", harass_min_mana_percent, 65);
        MENU_TOGGLE(HARASS_USE_Q, "kurisu:zeri:harass:use:q", harass_use_q, true);
        MENU_TOGGLE(HARASS_USE_Q_ANCHOR, "kurisu:zeri:harass:use:q:anchor", harass_use_q_anchor, true);
        MENU_TOGGLE(HARASS_USE_W, "kurisu:zeri:harass:use:w", harass_use_w, true);
        MENU_TOGGLE(HARASS_USE_E, "kurisu:zeri:harass:use:e", harass_use_e, false);
        MENU_SLIDER_I32(HARASS_MIN_SAFE_DISTANCE, "kurisu:zeri:harass:min_safe_distance", min_harass_safe_distance, 300);
        MENU_TOGGLE(HARASS_USE_E_DIVE_TURRET, "kurisu:zeri:harass:use:e:dive_turret", harass_use_e_dive_turret, false);

        MENU_PAGE(FARM_PAGE, "kurisu:zeri:farm");
        MENU_SLIDER_I32(FARM_MIN_MANA_PERCENT, "kurisu:zeri:farm:min:mana:percent", farm_min_mana_percent, 15);
        MENU_TOGGLE(FARM_USE_Q, "kurisu:zeri:farm:use:q", farm_use_q, true);

        MENU_PAGE(MECHANICS_PAGE, "kurisu:zeri:mechanics");
        MENU_TOGGLE(MECHANICS_PATHFINDING_E, "kurisu:zeri:mechanics:pathfinding:e", pathfinding_e, true);
        MENU_TOGGLE(MECHANICS_USE_E_JUMP, "kurisu:zeri:mechanics:use:e:jump", use_e_jump, true);
        MENU_SLIDER_I32(MECHANICS_MIN_SAFE_DISTANCE, "kurisu:zeri:mechanics:min_safe_distance", min_safe_distance, 250);
        MENU_SLIDER_I32(
            MECHANICS_USE_R_WHEN_ENEMIES_IN_RANGE, "kurisu:zeri:mechanics:use_r_when_enemies_in_range", use_r_when_enemies_in_range, 3);
        MENU_TOGGLE(MECHANICS_USE_E_FLEE, "kurisu:zeri:mechanics:use_e_flee", use_e_flee, true);
        // MENU_KEYBIND(MECHANICS_USE_E_JUMP_KEY, "kurisu:zeri:mechanics:use_e_jump_key", use_e_jump_key, VirtualKey::T);

        MENU_PAGE(RENDER_PAGE, "kurisu:zeri:render");
        MENU_TOGGLE(RENDER_Q_RANGE, "kurisu:zeri:render:q:range", render_q_range, true);
        MENU_TOGGLE(RENDER_W_RANGE, "kurisu:zeri:render:w:range", render_w_range, true);
        MENU_TOGGLE(RENDER_E_RANGE, "kurisu:zeri:render:e:range", render_e_range, true);
        MENU_TOGGLE(RENDER_R_RANGE, "kurisu:zeri:render:r:range", render_r_range, true);
        MENU_TOGGLE(RENDER_DAMAGE_INDICATOR, "kurisu:zeri:render:damage_indicator", render_damage_indicator, true);
        MENU_TOGGLE(RENDER_JUMP_SPOTS, "kurisu:zeri:render:jump_spots", render_jump_spots, true);

        // Empowered W
        MENU_TOGGLE(USE_EMPOWERED_W_COMBO, "kurisu:zeri:combo:use:empowered_w", use_empowered_w_combo, true);
        MENU_TOGGLE(USE_EMPOWERED_W_COMBO_ONLY, "kurisu:zeri:combo:use:empowered_w:only", use_empowered_w_combo_only, false);
        MENU_TOGGLE(USE_EMPOWERED_W_HARASS, "kurisu:zeri:harass:use:empowered_w", use_empowered_w_harass, true);
        MENU_TOGGLE(USE_EMPOWERED_W_HARASS_ONLY, "kurisu:zeri:harass:use:empowered_w:only", use_empowered_w_harass_only, true);

        void draw_menu(MenuContext* ctx) {
            const MenuPage page = ctx->current_page();
            if (page == MenuContext::PAGE_ROOT) {
                ctx->submenu(MAIN_PAGE, "[Kurisu] Zeri++");
                return;
            }

            if (page == MAIN_PAGE) {
                ctx->title("[Kurisu] Zeri++ v2.0");
                ctx->toggle(ENABLED, "Enable Plugin", &enabled);
                ctx->submenu(COMBO_PAGE, "Zeri: Combo");
                ctx->submenu(HARASS_PAGE, "Zeri: Harass");
                ctx->submenu(FARM_PAGE, "Zeri: Farm");
                ctx->submenu(MECHANICS_PAGE, "Zeri: Mechanics");
                ctx->submenu(RENDER_PAGE, "Zeri: Render");
            }

            if (page == COMBO_PAGE) {
                ctx->title("Zeri: Combo");
                ctx->toggle(COMBO_USE_Q, "Use Q", &use_q);
                ctx->toggle(COMBO_USE_Q_ANCHOR, "-> Find Anchor", &use_q_anchor);
                ctx->toggle(KILLSTEAL_ANCHOR_Q, "-> Secure Kill", &killsteal_anchor_q);
                ctx->toggle(COMBO_USE_W, "Use W", &use_w);
                ctx->toggle(USE_EMPOWERED_W_COMBO, "-> Use Extended W", &use_empowered_w_combo);
                ctx->toggle(USE_EMPOWERED_W_COMBO_ONLY, "-> Use Extended W Only", &use_empowered_w_combo_only);
                ctx->toggle(COMBO_USE_E, "Use E", &use_e);
                ctx->slider(COMBO_MIN_SAFE_DISTANCE, "-> Min Safe Distance", 0, 600, 10, &min_combo_safe_distance);
                ctx->toggle(COMBO_DIVE_TURRET, "-> Dive Turret", &combo_use_e_dive_turret);
                ctx->toggle(COMBO_USE_R, "Use R", &use_r);
                ctx->toggle(BLOCK_MANUAL_R, "-> Block Failed R", &block_manual_r);
                ctx->slider(MECHANICS_USE_R_WHEN_ENEMIES_IN_RANGE, "-> Auto Use (#)", 2, 5, 1, &use_r_when_enemies_in_range);
            }

            if (page == HARASS_PAGE) {
                ctx->title("Zeri: Harass");
                ctx->slider(HARASS_MIN_MANA_PERCENT, "Min Mana % for Harass", 0, 100, 1, &harass_min_mana_percent, true);
                ctx->toggle(HARASS_USE_Q, "Use Q", &harass_use_q);
                ctx->toggle(HARASS_USE_Q_ANCHOR, "-> Find Anchor", &harass_use_q_anchor);
                ctx->toggle(HARASS_USE_W, "Use W", &harass_use_w);
                ctx->toggle(USE_EMPOWERED_W_HARASS, "-> Use Extended W", &use_empowered_w_harass);
                ctx->toggle(USE_EMPOWERED_W_HARASS_ONLY, "-> Use Extended W Only", &use_empowered_w_harass_only);
                ctx->toggle(HARASS_USE_E, "Use E", &harass_use_e);
                ctx->slider(HARASS_MIN_SAFE_DISTANCE, "-> Min Safe Distance", 0, 600, 10, &min_harass_safe_distance);
                ctx->toggle(HARASS_USE_E_DIVE_TURRET, "-> Dive Turret", &harass_use_e_dive_turret);
            }

            if (page == FARM_PAGE) {
                ctx->title("Zeri: Farm");
                ctx->toggle(FARM_USE_Q, "Use Q", &farm_use_q);
            }

            if (page == MECHANICS_PAGE) {
                ctx->title("Zeri: Mechanics");
                ctx->toggle(MECHANICS_USE_E_FLEE, "Use E to Flee", &use_e_flee);
                ctx->toggle(MECHANICS_USE_E_JUMP, "Use E to Jumpspots", &use_e_jump);
                // ctx->keybind(MECHANICS_USE_E_JUMP_KEY, "-> Jumpspots Key", &use_e_jump_key);
            }

            if (page == RENDER_PAGE) {
                ctx->title("Zeri: Render");
                ctx->toggle(RENDER_Q_RANGE, "Draw Q", &render_q_range);
                ctx->toggle(RENDER_W_RANGE, "Draw W", &render_w_range);
                ctx->toggle(RENDER_E_RANGE, "Draw E", &render_e_range);
                ctx->toggle(RENDER_R_RANGE, "Draw R", &render_r_range);
                ctx->toggle(RENDER_DAMAGE_INDICATOR, "Draw Damage Indicator", &render_damage_indicator);
                ctx->toggle(RENDER_JUMP_SPOTS, "Draw Jump Spots", &render_jump_spots);
            }
        }
    };

    inline static Config* config = nullptr;

    static void on_draw_menu(MenuContext* ctx) {
        config->draw_menu(ctx);
    }
};  // namespace zeri
