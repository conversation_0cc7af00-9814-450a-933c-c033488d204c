<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{49cc4ee8-b871-468c-9327-2b0ea207eff0}</ProjectGuid>
    <RootNamespace>$(ProjectName)</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>ClangCL</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <EnableUnitySupport>true</EnableUnitySupport>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <GenerateManifest>false</GenerateManifest>
    <LinkIncremental>false</LinkIncremental>
    <TargetExt>.zp</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <SDLCheck>false</SDLCheck>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;PLUGIN_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdclatest</LanguageStandard_C>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FloatingPointModel>Fast</FloatingPointModel>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <ExceptionHandling>false</ExceptionHandling>
      <CreateHotpatchableImage>false</CreateHotpatchableImage>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <CallingConvention>FastCall</CallingConvention>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <AdditionalOptions>/Zc:threadSafeInit- -fno-builtin -Wno-unused-function -Werror=return-type %(AdditionalOptions)</AdditionalOptions>
      <EnableEnhancedInstructionSet>AdvancedVectorExtensions2</EnableEnhancedInstructionSet>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <ControlFlowGuard>false</ControlFlowGuard>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <DebugInformationFormat>None</DebugInformationFormat>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinFilesInUnityFile>1000</MinFilesInUnityFile>
      <ObjectFileName>$(IntDir)/%(RelativeDir)/</ObjectFileName>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableUAC>false</EnableUAC>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <NoEntryPoint>true</NoEntryPoint>
      <OutputFile>$(OutDir)$(TargetName).zp</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp" />
    <ClCompile Include="empowered_w.cpp" />
    <ClCompile Include="anchor_q.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
