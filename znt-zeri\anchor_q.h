#pragma once

#include "sdk.h"

namespace zeri {
    // --- Constants for Anchor Q ---
    constexpr f32 ANCHOR_Q_CHAIN_RANGE        = 650.0f;  // Chain range to additional enemies
    constexpr i32 ANCHOR_Q_MAX_CHAINS         = 4;       // Maximum number of chain targets
    constexpr f32 ANCHOR_Q_CHAIN_DAMAGE_RATIO = 0.3f;    // 30% AD damage for chain hits
    // --- End Constants for Anchor Q ---

    // Struct to store anchor Q solution
    struct AnchorQSolution {
        bool          found          = false;        // Whether a valid anchor solution was found
        AIBaseClient* primary_target = nullptr;      // Primary target to hit with Q
        Position      cast_position;                 // Position to cast Q at
        i32           chain_count           = 0;     // Number of enemies that will be hit by chains
        f32           total_expected_damage = 0.0f;  // Total expected damage from primary + chains
    };

    // Check if player has Zeri R buff (overcharged state)
    bool is_overcharged();

    // Find potential chain targets within range of a position
    Vec<AIBaseClient*> find_chain_targets(Position center, AIBaseClient* exclude_target = nullptr);

    // Calculate expected damage from anchor Q (primary + chains)
    f32 calc_anchor_q_damage(AIBaseClient* primary_target, const Vec<AIBaseClient*>& chain_targets);

    // Find the best anchor Q solution for the given spell parameters
    // This function evaluates potential primary targets and their chain potential
    AnchorQSolution find_best_anchor_q(const PredSpell& q_pred_spell, f32 range = FLT_MAX);

    // Try to cast anchor Q if conditions are met, returns true if successful
    bool try_cast_anchor_q(const PredSpell& q_pred_spell, f32 range = FLT_MAX);

}  // namespace zeri
