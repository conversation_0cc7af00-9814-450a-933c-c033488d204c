#include "empowered_w.h"

#include "main.h"

namespace zeri {
    constexpr f32 TWO_PI = 6.283185307179586f;  // Define TWO_PI

    // Check if a position is a wall
    bool is_position_a_wall(Position pos) {
        CollisionFlags flags = get_collision_flags(pos);
        return flags.is_wall();
    }

    // Function to find the best wall shot for empowered W
    // This function iterates through potential targets and directions to find an optimal scenario
    // where <PERSON><PERSON>'s W can hit a wall and then extend to hit an enemy champion.
    EmpoweredWSolution find_best_wall_shot(const PredSpell& initial_w_pred_spell) {
        EmpoweredWSolution best_solution;
        AIHeroClient*      player_hero = get_player();
        Position           player_pos  = player_hero->position();

        // Iterate through potential enemy targets
        for (const auto& enemy : get_enemy_heroes()) {
            // Check if the enemy is a valid target within the extended range of empowered W
            if (!is_valid_target(enemy, initial_w_pred_spell.range + EMPOWERED_W_EXTRA_RANGE)) {
                continue;
            }

            // Iterate over a number of directions around the player to find a suitable wall
            for (i32 i = 0; i < NUM_WALL_CHECK_DIRECTIONS; ++i) {
                f32      angle_rad = (TWO_PI / NUM_WALL_CHECK_DIRECTIONS) * i;
                Position direction_vector(cos(angle_rad), sin(angle_rad), 0.0f);  // Normalized 2D direction vector

                // Raycast to find a wall impact point for the initial W shot
                Position wall_impact_point;
                bool     wall_found = false;

                // Check progressively further along the direction vector until the range is reached
                for (f32 dist = WALL_CHECK_STEP_SIZE; dist <= initial_w_pred_spell.range; dist += WALL_CHECK_STEP_SIZE) {
                    Position far_point_in_direction = player_pos + direction_vector * initial_w_pred_spell.range;
                    Position check_pos              = player_pos.extend(far_point_in_direction, dist);

                    if (is_position_a_wall(check_pos)) {
                        // Validate that the initial W shot can reach the wall point without colliding with unintended objects.
                        // This uses the player's collision validation capabilities.
                        if (player_hero->validate_collisions(check_pos,
                                initial_w_pred_spell.cast_delay,
                                initial_w_pred_spell.speed,
                                initial_w_pred_spell.radius,
                                initial_w_pred_spell.collisions,
                                player_pos)) {
                            wall_impact_point = check_pos;
                            wall_found        = true;
                            break;  // Found a valid wall impact point along this direction
                        }
                    }
                }

                if (wall_found) {
                    // Calculate the time it takes for the initial W to hit the identified wall
                    f32 distance_to_wall      = player_pos.distance(wall_impact_point);
                    f32 initial_w_travel_time = 0.0f;

                    if (initial_w_pred_spell.speed > 0.001f) {
                        // Avoid division by zero for safety
                        initial_w_travel_time = distance_to_wall / initial_w_pred_spell.speed;
                    }

                    // Total delay before the empowered shot can be fired from the wall
                    f32 total_delay_before_empowered_shot = initial_w_pred_spell.cast_delay + initial_w_travel_time;

                    // Define the properties of the empowered W spell that will be cast from the wall
                    PredSpell empowered_w_pred_spell = {
                        .spell_type                  = PredSpellType::Linear,
                        .cast_delay                  = total_delay_before_empowered_shot,  // Adjusted delay includes travel time to wall
                        .radius                      = EMPOWERED_W_RADIUS,
                        .range                       = EMPOWERED_W_EXTRA_RANGE,
                        .speed                       = EMPOWERED_W_SPEED,
                        .hitbox_with_bounding_radius = true,
                        .collisions = PredCollisionType::Windwall,  // Empowered W typically only collides with windwall-like effects
                    };

                    // Predict the enemy's position when the empowered W (fired from the wall) would hit them
                    PredResult enemy_pred_for_spell_from_wall = enemy->predict_cast_position(&empowered_w_pred_spell, wall_impact_point);

                    // Check if the prediction is valid and has a high chance of hitting
                    if (enemy_pred_for_spell_from_wall.is_valid() && enemy_pred_for_spell_from_wall.hit_chance >= PredHitChance::High) {
                        // Determine the direction vector from the wall impact point to the predicted enemy cast position
                        Position aim_from_wall_dir_vec(enemy_pred_for_spell_from_wall.cast_position.x - wall_impact_point.x,
                            enemy_pred_for_spell_from_wall.cast_position.y - wall_impact_point.y,
                            0.0f);

                        // Calculate the squared length of the direction vector
                        f32 aim_from_wall_dir_len_sq =
                            aim_from_wall_dir_vec.x * aim_from_wall_dir_vec.x + aim_from_wall_dir_vec.y * aim_from_wall_dir_vec.y;

                        // Check if the direction vector is not too short to be meaningful
                        if (aim_from_wall_dir_len_sq > 0.001f) {
                            // Ensure it's not a zero vector before normalization
                            f32      aim_from_wall_dir_len_inv = 1.0f / sqrt(aim_from_wall_dir_len_sq);
                            Position normalized_aim_dir(aim_from_wall_dir_vec.x * aim_from_wall_dir_len_inv,
                                aim_from_wall_dir_vec.y * aim_from_wall_dir_len_inv,
                                0.0f);

                            // direction_vector is the original normalized direction of W to hit the wall.
                            // Calculate the dot product to check alignment between the initial W direction and the empowered W direction.
                            // This ensures the empowered shot continues roughly in the same direction as the initial shot.
                            f32 dot_product = normalized_aim_dir.x * direction_vector.x + normalized_aim_dir.y * direction_vector.y;

                            // Check for strong alignment (e.g., dot product close to 1, indicating a small angle difference)
                            if (dot_product > 0.98f)  // cos(angle) > 0.98 implies angle < approx 11.5 degrees
                            {
                                // Verify that the predicted enemy hit position is within the empowered W's extra range from the wall
                                f32 distance_to_enemy_hit_pos = wall_impact_point.distance(enemy_pred_for_spell_from_wall.position);
                                if (distance_to_enemy_hit_pos <= EMPOWERED_W_EXTRA_RANGE) {
                                    // If this solution is better than any previous one (or if no solution found yet), update best_solution.
                                    // "Better" is currently defined as targeting an enemy with lower health.
                                    if (!best_solution.found || (enemy->health() < best_solution.target->health())) {
                                        best_solution.found        = true;
                                        best_solution.wall_hit_pos = wall_impact_point;  // This is where to aim the *initial* W
                                        best_solution.predicted_enemy_pos =
                                            enemy_pred_for_spell_from_wall.position;  // Actual predicted enemy hit location
                                        best_solution.target = enemy;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (best_solution.found) {
                // Found a valid solution for this enemy, break out of the loop
                break;
            }
        }

        // Return the best solution found
        return best_solution;
    }

}  // namespace zeri