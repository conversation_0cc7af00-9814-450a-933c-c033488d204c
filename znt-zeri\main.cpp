#define SDK_IMPLEMENTATION
#include "main.h"

#include "anchor_q.h"
#include "config.h"
#include "coordinates.h"
#include "empowered_w.h"
#include "sdk.h"

namespace zeri {
    void on_update(void*) {
        // update the time and player
        time   = get_time();
        player = get_player();

        // rate limit the spell casts
        if (player->is_dead() || next_cast > time || !can_cast()) {
            return;
        }

        auto       passive = player->ammo();
        ActionMode mode    = get_action_mode();

        if (config->use_r && zeri_r->is_ready()) {
            i32 in_range = enemies_in_range(R_RANGE);
            if (in_range >= config->use_r_when_enemies_in_range) {
                if (zeri_r->cast_self()) {
                    next_cast = time + CAST_RATE;
                    return;
                }
            }
        }

        if (zeri_e->is_ready() && config->use_e_jump && mode.is(ActionMode::Flee)) {
            Position mouse_pos = get_world_cursor();
            for (const auto& spot : jump_spots) {
                if (mouse_pos.distance(spot.from) <= 400.0f) {
                    // Check if player is close enough to the "from" position
                    if (player->position().distance(spot.from) <= 25.0f) {
                        // Calculate the jump direction (from -> to)
                        Position jump_direction = spot.to - spot.from;
                        f32      jump_length    = sqrt(jump_direction.x * jump_direction.x + jump_direction.y * jump_direction.y);

                        if (jump_length > 0.001f) {
                            // Normalize jump direction
                            jump_direction.x /= jump_length;
                            jump_direction.y /= jump_length;

                            // Get player's intended direction (towards cursor)
                            Position player_direction = get_world_cursor() - player->position();
                            f32 player_dir_length = sqrt(player_direction.x * player_direction.x + player_direction.y * player_direction.y);

                            if (player_dir_length > 0.001f) {
                                // Normalize player direction
                                player_direction.x /= player_dir_length;
                                player_direction.y /= player_dir_length;

                                // Calculate dot product to check if directions align
                                f32 dot_product = jump_direction.x * player_direction.x + jump_direction.y * player_direction.y;

                                // Only jump if facing in the same general direction (dot product > 0.3 means roughly same direction)
                                if (dot_product > 0.3f) {
                                    if (zeri_e->cast_position(spot.to)) {
                                        return;
                                    }
                                }
                                // If facing away, don't jump - just stay in position or move closer
                            } else {
                                // If player has no movement direction, allow the jump
                                if (zeri_e->cast_position(spot.to)) {
                                    return;
                                }
                            }
                        }
                    } else {
                        // Move to the "from" position if not close enough
                        move(spot.from);
                        return;
                    }
                }
            }
        }

        if (config->use_e_flee && zeri_e->is_ready() && mode.is(ActionMode::Flee)) {
            // fallback if not walljumping
            Position cast_pos = get_world_cursor();
            if (zeri_e->cast_position(cast_pos)) {
                next_cast = time + CAST_RATE;
                return;
            }
        }

        if (config->use_r && zeri_r->is_ready() && mode.is(ActionMode::Combo)) {
            // Check if there are at least 2 enemies in range
            i32 in_range = enemies_in_range(player->auto_attack_range() - 200);
            if (in_range >= 2) {
                if (zeri_r->cast_self()) {
                    next_cast = time + CAST_RATE;
                    return;
                }
            }

            ts::TargetOptions options   = {.range = R_RANGE, .is_valid = nullptr};
            auto              ts_target = ts::get_target(options);
            if (ts_target && is_valid_target(ts_target, R_RANGE)) {
                // Calculate total damage and damage ratio to check for overkill
                f32 total_damage = combo_damage(ts_target);
                if (total_damage >= ts_target->health()) {
                    if (zeri_r->cast_self()) {
                        next_cast = time + CAST_RATE;
                        return;
                    }
                }
            }
        }

        if (config->use_q && q_ready() && config->killsteal_anchor_q) {
            // Check if any enemy is killable with chain damage when overcharged
            if (is_overcharged()) {
                for (auto enemy : get_enemy_heroes()) {
                    if (!is_valid_target(enemy))
                        continue;

                    // Calculate chain damage this enemy would take (30% AD)
                    f32 total_ad         = player->base_physical_damage() + player->bonus_physical_damage();
                    f32 chain_raw_damage = ANCHOR_Q_CHAIN_DAMAGE_RATIO * total_ad;
                    f32 chain_damage     = enemy->calc_physical_damage(player, chain_raw_damage);

                    // Check if chain damage can kill this enemy
                    if (chain_damage >= enemy->health()) {
                        // Find potential primary targets that could chain to this killable enemy
                        for (auto primary_target : get_enemy_heroes()) {
                            if (primary_target == enemy)
                                continue;  // Can't chain to yourself
                            if (!is_valid_target(primary_target, get_q_range()))
                                continue;

                            // Check if hitting primary_target would chain to our killable enemy
                            Vec<AIBaseClient*> chain_targets      = find_chain_targets(primary_target->position(), primary_target);
                            bool               chains_to_killable = false;
                            for (AIBaseClient* chain_target : chain_targets) {
                                if (chain_target == enemy) {
                                    chains_to_killable = true;
                                    break;
                                }
                            }

                            if (chains_to_killable) {
                                if (zeri_q->cast_position(primary_target->position())) {
                                    next_cast = time + CAST_RATE;
                                    return;
                                }
                            }
                        }

                        // Also check minions as primary targets
                        for (auto minion : get_minions()) {
                            if (!is_valid_minion(minion, get_q_range()))
                                continue;
                            if (minion->team() == player->team())
                                continue;  // Skip allied minions

                            // Check if hitting this minion would chain to our killable enemy
                            Vec<AIBaseClient*> chain_targets      = find_chain_targets(minion->position(), minion);
                            bool               chains_to_killable = false;
                            for (AIBaseClient* chain_target : chain_targets) {
                                if (chain_target == enemy) {
                                    chains_to_killable = true;
                                    break;
                                }
                            }

                            if (chains_to_killable) {
                                if (zeri_q->cast_position(minion->position())) {
                                    next_cast = time + CAST_RATE;
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Dash(E) logic for combo mode: uses pathfinding if enabled, otherwise dash towards cursor.
        if (config->use_e && zeri_e->is_ready() && mode.is(ActionMode::Combo)) {
            // Use pathfinding for E if enabled to find an optimal dash position.

            Position safe_tumble_pos;
            get_tumble_positions(player->position(), safe_tumble_pos);
            if (safe_tumble_pos.is_valid() && (!is_pos_under_turret(safe_tumble_pos) || config->combo_use_e_dive_turret)) {
                if (is_attack_cooldown() && zeri_e->cast_position(safe_tumble_pos)) {
                    next_cast = time + CAST_RATE;
                    return;
                }
            }
        }

        // Dash (E) logic for harass mode: respects mana and uses pathfinding if enabled.
        if (config->harass_use_e && zeri_e->is_ready() && mode.is(ActionMode::Harass)) {
            // Check mana percentage for harass Q
            f32 current_mana = player->mana();
            f32 max_mana     = player->max_mana();

            f32 current_mana_percent = (current_mana / max_mana) * 100.0f;
            if (current_mana_percent < config->harass_min_mana_percent) {
                return;  // Not enough mana
            }

            Position safe_tumble_pos;
            get_tumble_positions(player->position(), safe_tumble_pos, true);
            if (safe_tumble_pos.is_valid() && (!is_pos_under_turret(safe_tumble_pos) || config->harass_use_e_dive_turret)) {
                if (is_attack_cooldown() && zeri_e->cast_position(safe_tumble_pos)) {
                    next_cast = time + CAST_RATE;
                    return;
                }
            }
        }

        if (zeri_w->is_ready()) {
            PredSpell w_pred_spell = {
                .spell_type                  = PredSpellType::Linear,
                .cast_delay                  = 0.55f,
                .radius                      = 80.0f,
                .range                       = 1200.0f,
                .speed                       = 2500.0f,
                .hitbox_with_bounding_radius = true,
                .collisions                  = PredCollisionType::Minion | PredCollisionType::Hero | PredCollisionType::Windwall,
            };

            if (config->use_w && config->use_empowered_w_combo && mode == ActionMode::Combo) {
                EmpoweredWSolution wall_shot_solution = find_best_wall_shot(w_pred_spell);
                if (wall_shot_solution.found && wall_shot_solution.target) {
                    if (zeri_w->cast_position(wall_shot_solution.predicted_enemy_pos)) {
                        next_cast = time + CAST_RATE;
                        return;  // Empowered W cast successful4
                    }
                }
            }

            if (config->use_w && config->use_empowered_w_harass && mode == ActionMode::Harass) {
                if (player->mana() / player->max_mana() * 100.0f >= config->harass_min_mana_percent) {
                    EmpoweredWSolution wall_shot_solution = find_best_wall_shot(w_pred_spell);
                    if (wall_shot_solution.found && wall_shot_solution.target) {
                        if (zeri_w->cast_position(wall_shot_solution.predicted_enemy_pos)) {
                            next_cast = time + CAST_RATE;
                            return;  // Empowered W cast successful
                        }
                    }
                }
            }

            if (config->use_w && mode == ActionMode::Combo && !config->use_empowered_w_combo_only) {
                // Example: Predict and cast W at a target
                ts::TargetOptions options = {.range = w_pred_spell.range, .is_valid = nullptr};
                auto ts = ts::get_pred_target(options, w_pred_spell, [](const PredResult& pred) { return pred.hit_chance >= 2; });
                if (ts.target && is_valid_target(ts.target)) {
                    if (ts.target->position().distance(player->position()) > 600 || calc_w_damage(ts.target) >= ts.target->health()) {
                        if (zeri_w->cast_position(ts.pred.cast_position)) {
                            next_cast = time + CAST_RATE;
                            return;
                        }
                    }
                }
            }

            if (config->harass_use_w && mode == ActionMode::Harass && !config->use_empowered_w_harass_only) {
                if (player->mana() / player->max_mana() * 100.0f >= config->harass_min_mana_percent) {
                    ts::TargetOptions options = {.range = w_pred_spell.range, .is_valid = nullptr};
                    auto ts = ts::get_pred_target(options, w_pred_spell, [](const PredResult& pred) { return pred.hit_chance >= 2; });
                    if (ts.target && is_valid_target(ts.target)) {
                        if (ts.target->position().distance(player->position()) > 600 || calc_w_damage(ts.target) >= ts.target->health()) {
                            if (zeri_w->cast_position(ts.pred.cast_position)) {
                                next_cast = time + CAST_RATE;
                                return;
                            }
                        }
                    }
                }
            }
        }

        if (q_ready()) {
            auto projectile_speed = player->has_buff(SpellHash("ZeriR")) ? 3400.0f : 2600.0f;
            auto collision_type   = player->has_buff(SpellHash("ZeriESpecialRounds"))
                                        ? PredCollisionType::Windwall                                                         // Empowered Q
                                        : PredCollisionType::Minion | PredCollisionType::Hero | PredCollisionType::Windwall;  // Standard Q

            PredSpell q_pred_spell = {
                .spell_type                  = PredSpellType::Linear,  // Linear projectile
                .cast_delay                  = 0.25f,                  // 0.25s cast delay
                .radius                      = 80.0f,                  // 80 unit radius
                .range                       = get_q_range(),          // 1200 unit range
                .speed                       = projectile_speed,       // 2600 or 3400 unit speed
                .hitbox_with_bounding_radius = true,                   // Hitbox with bounding radius
                .collisions                  = collision_type,         // Collision type
            };

            // Get a prediction target for the Q spell
            ts::TargetOptions options = {.range = q_pred_spell.range, .is_valid = nullptr};

            if (config->use_q && mode == ActionMode::Combo) {
                auto ts = ts::get_pred_target(options, q_pred_spell, [](const PredResult& pred) { return pred.hit_chance >= 1; });
                if (ts.target && is_valid_target(ts.target)) {
                    if (zeri_q->cast_position(ts.pred.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;  // Standard Q cast successful
                    }
                }

                // Try anchor Q as fallback (has R buff)
                if (config->use_q_anchor && try_cast_anchor_q(q_pred_spell, q_pred_spell.range)) {
                    next_cast = time + CAST_RATE;
                    return;  // Anchor Q cast successful
                }
            }

            if (config->harass_use_q && mode == ActionMode::Harass) {
                auto ts = ts::get_pred_target(options, q_pred_spell, [](const PredResult& pred) { return pred.hit_chance >= 1; });
                if (ts.target && is_valid_target(ts.target)) {
                    if (zeri_q->cast_position(ts.pred.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;
                    }
                }

                // Try anchor Q as fallback (has R buff)
                if (config->harass_use_q_anchor && try_cast_anchor_q(q_pred_spell, q_pred_spell.range)) {
                    next_cast = time + CAST_RATE;
                    return;  // Anchor Q cast successful
                }
            }

            if (config->farm_use_q && (mode == ActionMode::Farm || mode == ActionMode::LastHit || mode == ActionMode::FastFarm)) {
                // --- Pass 1: Target enemy structures (Nexus, inhibitors, turrets) ---
                // First check nexus as highest priority target
                auto nexus = get_enemy_nexus();
                if (nexus && !nexus->is_dead() && nexus->is_targetable() &&
                    player->position().distance(nexus->position()) <= q_pred_spell.range) {
                    if (nexus->validate_collisions(nexus->position(),
                            q_pred_spell.cast_delay,
                            q_pred_spell.speed,
                            q_pred_spell.radius,
                            q_pred_spell.collisions,
                            player->position())) {
                        if (zeri_q->cast_position(nexus->position())) {
                            next_cast = time + CAST_RATE;
                            return;  // Successfully casted on enemy nexus
                        }
                    }
                }

                // Next check inhibitors
                for (auto inhibitor : get_enemy_inhibitors()) {
                    if (inhibitor && !inhibitor->is_dead() && inhibitor->is_targetable() &&
                        player->position().distance(inhibitor->position()) <= q_pred_spell.range) {
                        if (inhibitor->validate_collisions(inhibitor->position(),
                                q_pred_spell.cast_delay,
                                q_pred_spell.speed,
                                q_pred_spell.radius,
                                q_pred_spell.collisions,
                                player->position())) {
                            if (zeri_q->cast_position(inhibitor->position())) {
                                next_cast = time + CAST_RATE;
                                return;  // Successfully casted on enemy inhibitor
                            }
                        }
                    }
                }

                // Finally check turrets
                for (auto turret : get_enemy_turrets()) {
                    if (turret && !turret->is_dead() && turret->is_targetable() &&
                        player->position().distance(turret->position()) <= q_pred_spell.range) {
                        if (turret->validate_collisions(turret->position(),
                                q_pred_spell.cast_delay,
                                q_pred_spell.speed,
                                q_pred_spell.radius,
                                q_pred_spell.collisions,
                                player->position())) {
                            if (zeri_q->cast_position(turret->position())) {
                                next_cast = time + CAST_RATE;
                                return;  // Successfully casted on enemy turret
                            }
                        }
                    }
                }

                // If no structures were targeted, proceed with minion farming logic
                const auto&     all_farm_minions           = get_minions();
                AIMinionClient* prioritized_lasthit_target = nullptr;
                PredResult      prioritized_lasthit_pred_result;
                f32             best_lasthit_target_health_at_impact = FLT_MAX;

                // --- Pass 2: Attempt to find a prioritized last-hit ---
                for (AIMinionClient* minion : all_farm_minions) {
                    if (!is_valid_minion(minion, q_pred_spell.range))
                        continue;

                    // Modify collision type if it's a jungle monster in farm mode
                    PredSpell current_q_spell = q_pred_spell;  // Make a mutable copy
                    if ((mode.is(ActionMode::Farm) || mode.is(ActionMode::FastFarm)) && minion->is_jungle_monster()) {
                        current_q_spell.collisions = PredCollisionType::Windwall;  // Ignore minions and heroes
                    }

                    f32 q_damage_on_minion = calc_q_damage(minion);
                    if (q_damage_on_minion <= 0.0f)  // No damage or invalid calculation
                        continue;

                    f32 dist_to_minion = player->position().distance(minion->position());
                    f32 q_time =
                        (current_q_spell.speed > 0.001f && dist_to_minion > 0.001f) ? (dist_to_minion / current_q_spell.speed) : 0.0f;
                    f32 time_until_q_hits       = current_q_spell.cast_delay + q_time;
                    f32 minion_health_at_impact = minion->predicted_health(time_until_q_hits);

                    if (minion_health_at_impact > 0.0f && minion_health_at_impact <= q_damage_on_minion) {
                        // This minion is last-hittable by predicted health.
                        PredResult current_pred = minion->predict_cast_position(&current_q_spell, player->position());
                        if (current_pred.is_valid() && current_pred.hit_chance >= PredHitChance::Medium) {
                            // Check if the prediction's impact point is close to this minion's future position.
                            // This helps confirm the prediction is for *this* minion.
                            Position minion_future_pos             = minion->predict_path_position(time_until_q_hits);
                            f32      dist_sq_pred_to_minion_future = current_pred.position.distance_sqrd(minion_future_pos);

                            // Tolerance: Q radius + minion bounding radius (squared for efficiency)
                            f32 tolerance = current_q_spell.radius + minion->bounding_radius();
                            if (dist_sq_pred_to_minion_future < tolerance * tolerance) {
                                // This prediction seems to correctly target our intended last-hit minion.
                                // Prioritize the one with the lowest health at impact if multiple are found.
                                if (!prioritized_lasthit_target || minion_health_at_impact < best_lasthit_target_health_at_impact) {
                                    prioritized_lasthit_target           = minion;
                                    prioritized_lasthit_pred_result      = current_pred;
                                    best_lasthit_target_health_at_impact = minion_health_at_impact;
                                }
                            }
                        }
                    }
                }

                if (prioritized_lasthit_target) {
                    if (zeri_q->cast_position(prioritized_lasthit_pred_result.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;  // Successfully casted on a prioritized last-hit target
                    }
                }

                // --- Pass 3: If no prioritized last-hit was made (or cast failed), proceed to general push ---
                // This check is only relevant if we didn't already return after structure targeting
                if (mode == ActionMode::LastHit)  // Only push if not in LastHit only mode OR if structures weren't targeted.
                {
                    // If structures were targeted, we'd have returned already.
                    // So if we reach here in LastHit, it means no structures were hit, and we should not push.
                    return;
                }

                // q_pred_spell handles collisions, so it will aim for the first hitable minion.
                AIMinionClient* best_push_target = nullptr;
                PredResult      best_push_pred_result;
                f32             push_target_metric = FLT_MAX;  // Example: prioritize lowest current health for pushing

                for (AIMinionClient* minion : all_farm_minions) {
                    if (!is_valid_minion(minion, q_pred_spell.range))
                        continue;

                    // Modify collision type if it's a jungle monster in farm mode
                    PredSpell current_q_spell = q_pred_spell;  // Make a mutable copy
                    if ((mode.is(ActionMode::Farm) || mode.is(ActionMode::FastFarm)) && minion->is_jungle_monster()) {
                        current_q_spell.collisions = PredCollisionType::Windwall;  // Ignore minions and heroes
                    }

                    PredResult current_pred = minion->predict_cast_position(&current_q_spell, player->position());
                    if (current_pred.is_valid() && current_pred.hit_chance >= PredHitChance::Medium) {
                        f32 current_metric = minion->health();  // Simple heuristic for pushing
                        if (!best_push_target || current_metric < push_target_metric) {
                            best_push_target      = minion;        // The minion this prediction is *for*
                            best_push_pred_result = current_pred;  // The actual prediction result
                            push_target_metric    = current_metric;
                        }
                    }
                }

                if (best_push_target) {
                    if (zeri_q->cast_position(best_push_pred_result.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;  // Successfully casted for lane pushing
                    }
                }
            }
        }
    }

    void on_draw_ground(void*) {
        if (player->is_dead() || !config->enabled)
            return;

        if (config->render_w_range) {
            // Use the theme color with pulsing alpha
            const f32 pulse     = (sin(get_time() * 2.0f + 4.0f) + 1.0f) * 0.5f;
            const u8  alpha_val = static_cast<u8>(THEME_ALPHA + pulse * 80);  // Pulse between THEME_ALPHA and THEME_ALPHA+80 opacity

            renderer::draw_circle_3d(player->position(), 1200.0f, Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f, 8.0f, 16.0f);
        }

        if (config->render_r_range) {
            // Use the theme color with pulsing alpha
            const f32 pulse     = (sin(get_time() * 2.0f + 4.0f) + 1.0f) * 0.5f;
            const u8  alpha_val = static_cast<u8>(THEME_ALPHA + pulse * 80);  // Pulse between THEME_ALPHA and THEME_ALPHA+80 opacity

            renderer::draw_circle_3d(player->position(), R_RANGE, Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f, 8.0f, 16.0f);
        }

        if (config->render_e_range) {
            // Match the exact same color and style as safe dash positions
            const f32 pulse     = (sin(get_time() * 2.0f + 4.0f) + 1.0f) * 0.5f;
            const u8  alpha_val = static_cast<u8>(THEME_ALPHA + pulse * 80);  // Pulse between THEME_ALPHA and THEME_ALPHA+80 opacity

            // Use the same line thickness and segments as dash position circles
            renderer::draw_circle_3d(player->position(), E_RANGE, Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f, 8.0f, 16.0f);
        }

        if (config->render_q_range) {
            // Use the theme color with pulsing alpha - ensure it stays within valid range
            const f32 pulse = (sin(get_time() * 2.0f) + 1.0f) * 0.5f;  // Pulse between 0.0 and 1.0
            const u8  alpha_val =
                static_cast<u8>(THEME_ALPHA * 0.6f + pulse * THEME_ALPHA * 0.4f);  // Pulse between 60% and 100% of THEME_ALPHA
            auto range = get_q_range();

            renderer::draw_circle_3d(player->position(), range, Color(THEME_RED, THEME_GREEN, THEME_BLUE, alpha_val), 1.0f, 8.0f, 16.0f);
        }

        if (config->render_jump_spots) {
            draw_jump_spots();
        }
    }

    void on_draw_hud(void*) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        for (const auto& enemy : get_enemy_heroes()) {
            if (!enemy->is_dead() && enemy->is_visible() && enemy->is_on_screen()) {
                // Draw the damage indicator
                const f32 total_damage = combo_damage(enemy);
                draw_damage_indicator(total_damage, enemy);
            }
        }
    }

    void on_before_attack(OnBeforeAttackEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        auto passive = player->ammo();
        auto obj     = const_cast<AttackableUnit*>(event->target);
        auto minion  = obj->as_minion();
        auto hero    = obj->as_hero();

        if (minion && calc_passive_damage(minion) >= minion->health() && passive < 100.0f) {
            *(event->prevent) = false;
            return;
        }

        if (hero && calc_passive_damage(hero) >= hero->health()) {
            *(event->prevent) = false;
            return;
        }

        if (passive < 100.0f && minion && calc_passive_damage(minion) < minion->health()) {
            *(event->prevent) = true;
            return;
        }

        if (passive < 100.f && hero) {
            *(event->prevent) = true;
            return;
        }

        if (obj && obj->is_turret()) {
            if (passive < 100.0f) {
                *(event->prevent) = true;
                return;
            }
        }

        *(event->prevent) = false;
    }

    void on_validate_and_cast_spell(OnValidateAndCastSpellhEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        if (config->block_manual_r && event->manual && event->spell_data->spell_hash() == SpellHash("ZeriR")) {
            if (enemies_in_range(R_RANGE) == 0) {
                *(event->prevent) = true;
                return;
            }
        }
    }

    void on_load() {
        player = get_player();
        if (!player->is("Zeri")) {
            return;
        }

        zeri_q = player->get_spell(SpellSlot::Q);
        zeri_w = player->get_spell(SpellSlot::W);
        zeri_e = player->get_spell(SpellSlot::E);
        zeri_r = player->get_spell(SpellSlot::R);
        config = new Config();

        register_module("[Kurisu] Zeri++",
            {.on_draw_ground                = on_draw_ground,
                .on_draw_hud                = on_draw_hud,
                .on_draw_menu               = on_draw_menu,
                .on_update                  = on_update,
                .on_before_attack           = on_before_attack,
                .on_validate_and_cast_spell = on_validate_and_cast_spell});
    }
}  // namespace zeri

ENTRY({ zeri::on_load(); });
