#pragma once

#include "sdk.h"

namespace zeri {
    // --- Constants for Empowered W ---
    constexpr f32 EMPOWERED_W_EXTRA_RANGE   = 1600.0f;
    constexpr f32 EMPOWERED_W_RADIUS        = 200.0f;
    constexpr f32 EMPOWERED_W_SPEED         = 2500.0f;  // Speed of the laser after hitting terrain. Adjust if different from normal W.
    constexpr i32 NUM_WALL_CHECK_DIRECTIONS = 36;       // Check every 10 degrees
    constexpr f32 WALL_CHECK_STEP_SIZE      = 25.0f;
    // --- End Constants for Empowered W ---

    // Struct to store the best wall shot solution
    struct EmpoweredWSolution {
        bool          found = false;        // Whether a valid solution was found
        Position      wall_hit_pos;         // Position to aim the initial W
        Position      predicted_enemy_pos;  // Predicted enemy position when the empowered W hits
        AIHeroClient* target = nullptr;     // Target champion that the empowered W will hit
    };

    // Check if a position is a wall
    bool is_position_a_wall(Position pos);

    // Function to find the best wall shot for empowered W
    // This function iterates through potential targets and directions to find an optimal scenario
    // where <PERSON><PERSON>'s W can hit a wall and then extend to hit an enemy champion.
    EmpoweredWSolution find_best_wall_shot(const PredSpell& initial_w_pred_spell);

}  // namespace zeri