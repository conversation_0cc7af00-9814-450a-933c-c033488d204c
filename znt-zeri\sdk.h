#pragma once

#include <stdarg.h>
#include <immintrin.h>
#include <initializer_list>
#include <new>

#define NOINLINE __attribute__((noinline))
#define FINLINE inline __forceinline __attribute__((always_inline))

#define FLT_EPSILON __FLT_EPSILON__

using i8 = __int8;
using i16 = __int16;
using i32 = __int32;
using i64 = __int64;
using u8 = unsigned __int8;
using u16 = unsigned __int16;
using u32 = unsigned __int32;
using u64 = unsigned __int64;
using f32 = float;
using f64 = double;
using isize = i64;
using usize = u64;
using uptr = u64;

extern "C" i32 _fltused = 0;

static int atexit(void(__cdecl* func)(void)) {
    return 0;
}

inline constexpr u8 XOR_KEY[] = {
    0xDC, 0x83, 0xB3, 0x76, 0x16, 0x89, 0x69, 0xBA, 0xC4, 0x52, 0x66, 0xA1, 0x51, 0xBC, 0x21, 0x34, 0x30, 0x18, 0xB9, 0x43, 0x2E, 0xE9,
    0x26, 0x44, 0xAA, 0x28, 0x17, 0x6B, 0xD9, 0xCC, 0x2F, 0xF5, 0x76, 0x80, 0xDD, 0x55, 0x9B, 0xA0, 0x2C, 0xA9, 0x97, 0xC4, 0x7E, 0x34,
    0x58, 0x1F, 0x76, 0x88, 0xB2, 0x64, 0x8B, 0x89, 0xAF, 0x53, 0x85, 0xA3, 0x81, 0x5C, 0x3D, 0x19, 0x9A, 0xD5, 0xFC, 0x50, 0x49, 0x19,
    0xE0, 0xEA, 0xF3, 0xB8, 0x1F, 0xA3, 0xE4, 0x5C, 0xC8, 0xF5, 0x30, 0xE7, 0xF5, 0x58, 0x24, 0x31, 0xE2, 0x49, 0x54, 0x1C, 0xF3, 0x6B,
    0x91, 0xEA, 0x5A, 0x53, 0x4B, 0x22, 0xB2, 0xC0, 0xE9, 0x69, 0x2C, 0xBE, 0xF7, 0xB1, 0x4F, 0x25, 0xE3, 0x52, 0xD9, 0xBF, 0x2A, 0x5E,
    0x6C, 0xE2, 0xBB, 0x83, 0x6C, 0x20, 0xC8, 0xA5, 0x6D, 0x4E, 0x7D, 0x5B, 0x1C, 0xC2, 0x77, 0x95, 0x42, 0xDD, 0xF2, 0xDA, 0xFC, 0xAA,
    0x5D, 0xA3, 0x5A, 0x53, 0xF8, 0xE7, 0x5A, 0x28, 0x9A, 0xE8, 0x67, 0x30, 0x80, 0xC9, 0x6E, 0x23, 0x53, 0x97, 0x56, 0x2F, 0x5E, 0xC8,
    0xA3, 0x7F, 0xAC, 0x29, 0xBA, 0x85, 0x34, 0xDF, 0xBA, 0x37, 0x74, 0x6B, 0x29, 0xFB, 0xB0, 0xD3, 0xAC, 0x7F, 0x5A, 0xCE, 0x71, 0x77,
    0x4A, 0xD8, 0x6A, 0xD6, 0xEA, 0x57, 0x62, 0xFE, 0x2E, 0x51, 0x3F, 0xF2, 0xAB, 0x40, 0xA9, 0x9A, 0x9D, 0xD2, 0x26, 0x97, 0x82, 0xEE,
    0x3C, 0x4D, 0x36, 0xC3, 0xAD, 0x73, 0xD5, 0xDB, 0x21, 0xF5, 0xD0, 0x34, 0x28, 0x71, 0xFF, 0xD9, 0x1C, 0x9F, 0x3F, 0x47, 0x83, 0x8B,
    0x8C, 0x1E, 0x91, 0xCE, 0xE7, 0xFE, 0x22, 0x56, 0x5A, 0x26, 0x1D, 0x3B, 0x1F, 0x46, 0xBA, 0xCC, 0x67, 0x51, 0x81, 0x37, 0x6E, 0x16,
    0xC3, 0x5E, 0xF7, 0xEB, 0x8B, 0x57, 0x9B, 0x47, 0xAF, 0xAD, 0x39, 0x47, 0xF4, 0x37, 0x91, 0x30, 0x41, 0xE4, 0xE2, 0x1E, 0x3C, 0x8A,
    0x21, 0x79, 0xA4, 0x7A, 0x1F, 0x6E, 0xB1, 0xCF, 0xED, 0xBD, 0x6D, 0x98, 0xA9, 0xDB, 0xFF, 0x62, 0xC5, 0x83, 0x15, 0xCC, 0xAC, 0xD6,
    0xFD, 0xF2, 0x73, 0x33, 0xAF, 0xB2, 0x78, 0xE6, 0x93, 0xD7, 0x38, 0x14, 0xA6, 0x9F, 0x14, 0xB4, 0x69, 0x67, 0x57, 0xCF, 0x37, 0x67,
    0xA3, 0x69, 0x17, 0x99, 0x9A, 0x18, 0xF4, 0x29, 0x2E, 0x78, 0x46, 0x5C, 0x4A, 0xB1, 0xFE, 0xA3, 0x47, 0x81, 0x7B, 0xE1, 0x86, 0x85,
    0x8B, 0x89, 0xE6, 0x97, 0x36, 0xC5, 0x48, 0x1D, 0xA6, 0x1E, 0x6A, 0x3A, 0x5B, 0x3D, 0xA0, 0xDA, 0x44, 0x49, 0x4F, 0xF3, 0x30, 0x29,
    0x70, 0xAE, 0xCB, 0x6D, 0xE6, 0x23, 0x27, 0xC6, 0x7D, 0x34, 0x64, 0x49, 0xFE, 0xF9, 0x82, 0xF0, 0x7D, 0x49, 0x1F, 0x5D, 0x6F, 0xD7,
    0x42, 0x6D, 0x7C, 0xD2, 0xCC, 0xCB, 0xB3, 0x1B, 0xF4, 0x5F, 0xB7, 0xE3, 0xE9, 0xB9, 0xC1, 0xF8, 0x5C, 0x3D, 0xD1, 0x60, 0x49, 0x18,
    0xF6, 0xC4, 0x1B, 0x88, 0xC9, 0xA6, 0x59, 0xAF, 0x98, 0x60, 0x68, 0xF7, 0xF3, 0x43, 0x47, 0x24, 0xAB, 0x72, 0x35, 0xC8, 0x4E, 0x58,
    0x57, 0x14, 0x61, 0x6A, 0xBA, 0xDC, 0xF9, 0x6A, 0x94, 0x92, 0x92, 0xB6, 0xEC, 0xD5, 0xE9, 0xDB, 0xA0, 0xED, 0x8E, 0x9E, 0x5D, 0xEB,
    0x83, 0x4E, 0x80, 0xBB, 0xF4, 0x99, 0x74, 0x9F, 0xED, 0x39, 0xB0, 0x6C, 0xAC, 0x81, 0x6D, 0xD8, 0x24, 0xEF, 0x8A, 0x7A, 0x74, 0xC3,
    0x3A, 0xEC, 0x61, 0xBF, 0xEE, 0xF1, 0xBF, 0x45, 0x6B, 0x22, 0xB1, 0x31, 0x7B, 0xA1, 0xB4, 0xD2, 0x4E, 0xA1, 0x4F, 0xFA, 0x7A, 0x99,
    0xE3, 0xDD, 0x47, 0x88, 0xA4, 0x7A, 0xD3, 0xD7, 0x93, 0xA3, 0xCB, 0x97, 0xBF, 0x7E, 0x30, 0x2A, 0xDB, 0xBB, 0xBE, 0xBE, 0xCA, 0xE3,
    0x1F, 0x18, 0x61, 0x16, 0xA7, 0xA8,
};

template<typename T> using InitializerList = std::initializer_list<T>;

FINLINE static void* _memset(void* dst, i32 value, usize size) {
    char* dst_bytes = (char*)dst;
    while (size--) {
        *dst_bytes++ = (char)value;
    }
    return dst;
}

extern "C" void* memset(void* dst, i32 value, usize size) {
    return _memset(dst, value, size);
}

#define memset(dst, value, size) _memset(dst, value, size)

FINLINE static void* _memcpy(void* dst, const void* src, usize size) {
    char* dst_bytes = (char*)dst;
    const char* src_bytes = (const char*)src;
    while (size--) {
        *dst_bytes++ = *src_bytes++;
    }
    return dst;
}

extern "C" void* memcpy(void* dst, const void* src, usize size) {
    return _memcpy(dst, src, size);
}

#define memcpy(dst, src, size) _memcpy(dst, src, size)

FINLINE static void* memmove(void* dst, const void* src, usize size) {
    char* dst_bytes = (char*)dst;
    const char* src_bytes = (const char*)src;
    if (dst_bytes < src_bytes) {
        while (size--) {
            *dst_bytes++ = *src_bytes++;
        }
    } else {
        dst_bytes += size;
        src_bytes += size;
        while (size--) {
            *--dst_bytes = *--src_bytes;
        }
    }
    return dst;
}

FINLINE static usize strlen(const char* str) {
    const char* ptr = str;
    while (*ptr != 0) {
        ptr++;
    }
    return ptr - str;
}

#define FLT_MAX 3.402823466e+38f

constexpr char ASCII_LOWER[128] = {
    0,   1,   2,   3,   4,   5,   6,   7,   8,   9,   10,  11,  12,  13,  14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25,
    26,  27,  28,  29,  30,  31,  32,  33,  34,  35,  36,  37,  38,  39,  40,  41,  42,  43,  44,  45,  46,  47,  48,  49,  50,  51,
    52,  53,  54,  55,  56,  57,  58,  59,  60,  61,  62,  63,  64,  97,  98,  99,  100, 101, 102, 103, 104, 105, 106, 107, 108, 109,
    110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 91,  92,  93,  94,  95,  96,  97,  98,  99,  100, 101, 102, 103,
    104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127,
};

template<u32 S> consteval u64 fnv1a64(const char (&value)[S]) {
    u64 hash = 14695981039346656037ui64;
    for (u32 i = 0; i < S - 1; i++) {
        hash = hash ^ value[i];
        hash = hash * 1099511628211ui64;
    }
    return hash;
}

struct FNV1A64 {
   private:

    u64 hash = 0;

   public:

    consteval FNV1A64(u64 hash): hash(hash) {}

    template<u32 S> consteval FNV1A64(const char (&value)[S]): hash(fnv1a64<S>(value)) {}

    inline operator u64() const {
        return hash;
    }
};

template<u32 S> consteval u32 fnv1a32_low(const char (&value)[S]) {
    u32 hash = 0x811C9DC5;
    for (u32 i = 0; i < S - 1; i++) {
        hash = hash ^ ASCII_LOWER[static_cast<u8>(value[i])];
        hash = hash * 0x1000193;
    }
    return hash;
}

using FnLoadFunction = void* (*)(FNV1A64 hash);
inline FnLoadFunction load_function = nullptr;

#define FN(ret, name, ...) static const auto fn = (ret(*)(__VA_ARGS__))(load_function(name));

#define COMBINE(...) __VA_ARGS__
#define STATIC_FUNCTION(type, name, args, params, key)              \
    static type name(args) {                                        \
        static const auto fn = (type(*)(args))(load_function(key)); \
        return fn(params);                                          \
    }

STATIC_FUNCTION(void*, malloc, size_t size, size, "core:mem:malloc");
STATIC_FUNCTION(void, free, void* block, block, "core:mem:free");

void* operator new(size_t size);
void* operator new[](size_t size);
void operator delete(void* block);
void operator delete(void* block, size_t size);
void operator delete[](void* block);

template<class T> struct Vec {
   private:

    T* data = nullptr;
    u32 length = 0;
    u32 capacity = 0;

   public:

    constexpr Vec() = default;

    Vec(const u32 capacity): capacity(capacity) {
        data = static_cast<T*>(malloc(sizeof(T) * capacity));
    }

    Vec(const InitializerList<T>& list): capacity(u32(list.size())) {
        data = static_cast<T*>(malloc(sizeof(T) * capacity));
        for (const T& item: list) {
            new (data + length) T(item);
            length++;
        }
    }

    Vec(const Vec& other): capacity(other.length), length(other.length) {
        data = static_cast<T*>(malloc(sizeof(T) * capacity));
        for (u32 i = 0; i < length; i++) {
            new (data + i) T(other.data[i]);
        }
    }

    Vec(Vec&& other): data(other.data), length(other.length), capacity(other.capacity) {
        other.data = nullptr;
        other.length = 0;
        other.capacity = 0;
    }

    ~Vec() {
        if (!data) {
            return;
        }
        clear();
        free(data);
        data = nullptr;
        capacity = 0;
    }

    inline void clear() {
        for (u32 i = 0; i < length; i++) {
            data[i].~T();
        }
        length = 0;
    }

    inline u32 calc_capacity() const {
        if (capacity == 0) {
            return 2;
        } else if (capacity > 1024) {
            return capacity + capacity / 4;
        } else {
            return capacity * 2;
        }
    }

    void reserve(const u32 new_capacity) {
        if (capacity >= new_capacity) {
            return;
        }
        T* new_data = static_cast<T*>(malloc(sizeof(T) * new_capacity));
        if (length != 0) {
            memcpy(new_data, data, sizeof(T) * length);
        }
        if (data) {
            free(data);
        }
        data = new_data;
        capacity = new_capacity;
    }

    Vec& operator=(const Vec& other) {
        if (this != &other) {
            clear();
            reserve(other.length);
            length = other.length;
            for (u32 i = 0; i < length; i++) {
                new (data + i) T(other.data[i]);
            }
        }
        return *this;
    }

    Vec& operator=(Vec&& other) {
        if (this != &other) {
            clear();
            if (data) {
                free(data);
            }
            data = other.data;
            length = other.length;
            capacity = other.capacity;
            other.data = nullptr;
            other.length = 0;
            other.capacity = 0;
        }
        return *this;
    }

    inline u32 len() const {
        return length;
    }

    inline u32 cap() const {
        return capacity;
    }

    inline bool empty() const {
        return length == 0;
    }

    inline T* begin() {
        return data;
    }

    inline const T* begin() const {
        return data;
    }

    inline T* end() {
        return data + length;
    }

    inline const T* end() const {
        return data + length;
    }

    void resize(const u32 new_length) {
        if (new_length > length) {
            if (new_length > capacity) {
                reserve(new_length);
            }
            for (u32 i = length; i < new_length; i++) {
                new (data + i) T();
            }
        } else {
            for (u32 i = new_length; i < length; i++) {
                data[i].~T();
            }
        }
        length = new_length;
    }

    void push(const T& item) {
        if (length == capacity) {
            reserve(calc_capacity());
        }
        new (data + length) T(item);
        length++;
    }

    void push(T&& item) {
        if (length == capacity) {
            reserve(calc_capacity());
        }
        new (data + length) T(static_cast<T&&>(item));
        length++;
    }

    template<typename... Args> void emplace(Args&&... args) {
        if (length == capacity) {
            reserve(calc_capacity());
        }
        new (data + length) T(forward<Args>(args)...);
        length++;
    }

    void insert(const T* iterator, const T& item) {
        const u32 delta = u32(iterator - data);
        if (delta >= length) {
            push(item);
            return;
        }
        if (length == capacity) {
            reserve(calc_capacity());
        }
        memmove(iterator + 1, iterator, (length - (iterator - data)) * sizeof(T));
        new (iterator) T(item);
        length++;
    }

    void insert(const T* iterator, T&& item) {
        const u32 delta = u32(iterator - data);
        if (delta >= length) {
            push(static_cast<T&&>(item));
            return;
        }
        if (length == capacity) {
            reserve(calc_capacity());
        }
        memmove(iterator + 1, iterator, (length - (iterator - data)) * sizeof(T));
        new (iterator) T(static_cast<T&&>(item));
        length++;
    }

    T pop() {
        length--;
        T item = static_cast<T&&>(data[length]);
        data[length].~T();
        return item;
    }

    T* erase(const T* iterator) {
        const u32 delta = u32(iterator - data);
        data[delta].~T();
        memmove(data + delta, data + delta + 1, (length - delta - 1) * sizeof(T));
        length--;
        return data + delta;
    }

    bool remove(const T& item) {
        for (u32 i = 0; i < length; i++) {
            if (data[i] == item) {
                data[i].~T();
                if (length > 1) {
                    memmove(data + i, data + i + 1, sizeof(T) * (length - i - 1));
                }
                length--;
                return true;
            }
        }
        return false;
    }

    bool remove_by_index(const u32 i) {
        if (i >= length) {
            return false;
        }
        data[i].~T();
        if (length > i + 1) {
            memmove(data + i, data + i + 1, sizeof(T) * (length - i - 1));
        }
        length--;
        return true;
    }

    inline T& operator[](const u32 index) {
        return data[index];
    }

    inline const T& operator[](const u32 index) const {
        return data[index];
    }

    inline T& first() {
        return data[0];
    }

    inline const T& first() const {
        return data[0];
    }

    inline T& last() {
        return data[length - 1];
    }

    inline const T& last() const {
        return data[length - 1];
    }

    bool contains(const T& item) const {
        for (u32 i = 0; i < length; i++) {
            if (data[i] == item) {
                return true;
            }
        }
        return false;
    }

    template<typename Compare> void quick_sort(Compare compare, i32 low, i32 high) {
        i32 i = low;
        i32 j = high;
        T& pivot = data[(i + j) / 2];
        while (i <= j) {
            while (compare(data[i], pivot) < 0) {
                i++;
            }
            while (compare(data[j], pivot) > 0) {
                j--;
            }
            if (i <= j) {
                T temp = data[i];
                data[i] = data[j];
                data[j] = temp;
                i++;
                j--;
            }
        }
        if (low < j) {
            quick_sort(compare, low, j);
        }
        if (i < high) {
            quick_sort(compare, i, high);
        }
    }

    /**
     * @brief Sorts the elements in the vector using the provided comparison function.
     *
     * This function sorts the elements in the vector in place using the quick sort algorithm.
     *
     * @tparam Compare A comparison function object which returns:
     *                 - 0 if the elements are equal
     *                 - A positive value if the first element is greater than the second
     *                 - A negative value if the first element is less than the second
     *
     * @param compare The comparison function to use for sorting the elements.
     *
     * @code
     * Vec<int> vec = {3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5};
     * vec.sort([](int a, int b) {
     *     return a - b; // Sort in ascending order
     * });
     * @endcode
     */
    template<typename Compare> void sort(Compare compare) {
        if (length < 2) {
            return;
        }
        quick_sort(compare, 0, length - 1);
    }
};

namespace ztd {

    static inline constexpr u64 xxhash_rotate_left_64(u64 x, int r) {
        return (x << r) | (x >> (64 - r));
    }

    constexpr u64 xxhash_prime64_1 = 11400714785074694791ULL;
    constexpr u64 xxhash_prime64_2 = 14029467366897019727ULL;
    constexpr u64 xxhash_prime64_3 = 1609587929392839161ULL;
    constexpr u64 xxhash_prime64_4 = 9650029242287828579ULL;
    constexpr u64 xxhash_prime64_5 = 2870177450012600261ULL;

    static constexpr u64 xxhash64(const void* data, size_t length, u64 seed = 0) {
        const unsigned char* p = static_cast<const unsigned char*>(data);
        const unsigned char* b_end = p + length;
        u64 h64 = 0;

        if (length >= 32) {
            u64 v1 = seed + xxhash_prime64_1 + xxhash_prime64_2;
            u64 v2 = seed + xxhash_prime64_2;
            u64 v3 = seed;
            u64 v4 = seed - xxhash_prime64_1;

            do {
                v1 += (*(u64*)p) * xxhash_prime64_2;
                p += 8;
                v1 = xxhash_rotate_left_64(v1, 31);
                v1 *= xxhash_prime64_1;

                v2 += (*(u64*)p) * xxhash_prime64_2;
                p += 8;
                v2 = xxhash_rotate_left_64(v2, 31);
                v2 *= xxhash_prime64_1;

                v3 += (*(u64*)p) * xxhash_prime64_2;
                p += 8;
                v3 = xxhash_rotate_left_64(v3, 31);
                v3 *= xxhash_prime64_1;

                v4 += (*(u64*)p) * xxhash_prime64_2;
                p += 8;
                v4 = xxhash_rotate_left_64(v4, 31);
                v4 *= xxhash_prime64_1;
            } while (p + 32 <= b_end);

            h64 = (v1 << 1) + (v2 << 7) + (v3 << 12) + (v4 << 18);
        } else {
            h64 = seed + xxhash_prime64_5;
        }

        h64 += static_cast<u64>(length);

        while (p + 8 <= b_end) {
            u64 k1 = (*(u64*)p) * xxhash_prime64_2;
            k1 = xxhash_rotate_left_64(k1, 31);
            k1 *= xxhash_prime64_1;
            h64 ^= k1;
            h64 = xxhash_rotate_left_64(h64, 27) * xxhash_prime64_1 + xxhash_prime64_4;
            p += 8;
        }
        while (p < b_end) {
            u64 k1 = (*p++) * xxhash_prime64_5;
            k1 = xxhash_rotate_left_64(k1, 11);
            k1 *= xxhash_prime64_1;
            h64 ^= k1;
            h64 = xxhash_rotate_left_64(h64, 23) * xxhash_prime64_2 + xxhash_prime64_3;
        }

        h64 ^= h64 >> 33;
        h64 *= xxhash_prime64_2;
        h64 ^= h64 >> 29;
        h64 *= xxhash_prime64_3;
        h64 ^= h64 >> 32;
        return h64;
    }

    template<typename T> static inline u64 hash_map_key_hash(const T& data) {
        return xxhash64(&data, sizeof(data));
    }

    static inline u64 hash_map_key_hash(char* data) {
        if (!data) {
            return 0;
        }
        return xxhash64(data, strlen(data));
    }

    static inline u64 hash_map_key_hash(const char* data) {
        if (!data) {
            return 0;
        }
        return xxhash64(data, strlen(data));
    }
} // namespace ztd

template<typename K, typename V> struct HashMap {
   private:

    struct Pair {
        bool used = false;
        bool was_used = false;
        K key;
        V value;

        inline Pair() = default;

        inline ~Pair() {
            if (used) {
                key.~K();
                value.~V();
                used = false;
            }
        }

        inline void set(const K& k, const V& v) {
            if (used) {
                key.~K();
                value.~V();
            }
            new (&key) K(k);
            new (&value) V(v);
            used = true;
            was_used = true;
        }

        inline void set(const K& k, V&& v) {
            if (used) {
                key.~K();
                value.~V();
            }
            new (&key) K(k);
            new (&value) V(static_cast<V&&>(v));
            used = true;
            was_used = true;
        }

        inline void set(K&& k, const V& v) {
            if (used) {
                key.~K();
                value.~V();
            }
            new (&key) K(static_cast<K&&>(k));
            new (&value) V(v);
            used = true;
            was_used = true;
        }

        inline void set(K&& k, V&& v) {
            if (used) {
                key.~K();
                value.~V();
            }
            new (&key) K(static_cast<K&&>(k));
            new (&value) V(static_cast<V&&>(v));
            used = true;
            was_used = true;
        }
    };

    Pair* table;
    u32 length = 0;
    u32 capacity = 0;

    inline u32 probe(const u32 index) const {
        return (index + 1) & (capacity - 1);
    }

    inline u32 next_power_of_two(u32 x) const {
        x--;
        x |= x >> 1;
        x |= x >> 2;
        x |= x >> 4;
        x |= x >> 8;
        return x + 1;
    }

   public:

    inline void resize() {
        const u32 new_capacity = capacity == 0 ? 8 : capacity << 1;
        Pair* new_table = static_cast<Pair*>(malloc(new_capacity * sizeof(Pair)));
        memset(new_table, 0, new_capacity * sizeof(Pair));
        for (u32 i = 0; i < new_capacity; i++) {
            new_table[i].used = false;
        }
        if (table) {
            for (u32 i = 0; i < capacity; i++) {
                if (table[i].used) {
                    const u32 hash = ztd::hash_map_key_hash(table[i].key);
                    u32 index = hash & (new_capacity - 1);
                    while (new_table[index].used) {
                        index = (index + 1) & (new_capacity - 1);
                    }
                    new_table[index].set(static_cast<K&&>(table[i].key), static_cast<V&&>(table[i].value));
                }
            }
            for (u32 i = 0; i < capacity; i++) {
                table[i].~Pair();
            }
            free(table);
        }
        table = new_table;
        capacity = new_capacity;
    }

    inline u32 get_insert_index(const K& k) {
        if ((length + 1) * 2 >= capacity) {
            resize();
        }
        const u64 hash = ztd::hash_map_key_hash(k);
        u32 index = hash & (capacity - 1);
        while (table[index].used && !(table[index].key == k)) {
            index = probe(index);
        }
        return index;
    }

   public:

    struct Entry {
        const K& key;
        V& value;
    };

    class Iterator {
       private:

        HashMap* map;
        u32 index;

        void advance_to_used() {
            while (index < map->capacity && !map->table[index].used) {
                index++;
            }
        }

       public:

        Iterator(HashMap* map, u32 index): map(map), index(index) {
            advance_to_used();
        }

        Iterator& operator++() {
            ++index;
            advance_to_used();
            return *this;
        }

        bool operator==(const Iterator& other) const {
            return index == other.index && map == other.map;
        }

        bool operator!=(const Iterator& other) const {
            return !(*this == other);
        }

        const Entry operator*() const {
            return Entry{map->table[index].key, map->table[index].value};
        }
    };

    Iterator begin() {
        return Iterator(this, 0);
    }

    Iterator end() {
        return Iterator(this, capacity);
    }

    HashMap(u32 init_capacity = 8): capacity(next_power_of_two(init_capacity)) {
        table = static_cast<Pair*>(malloc(capacity * sizeof(Pair)));
        memset(table, 0, capacity * sizeof(Pair));
        for (u32 i = 0; i < capacity; i++) {
            table[i].used = false;
        }
    }

    struct InitEntry {
        K key;
        V value;

        InitEntry(const K& key, const V& value): key(key), value(value) {}

        InitEntry(K&& key, const V& value): key(static_cast<K&&>(key)), value(value) {}

        InitEntry(const K& key, V&& value): key(key), value(static_cast<V&&>(value)) {}

        InitEntry(K&& key, V&& value): key(static_cast<K&&>(key)), value(static_cast<V&&>(value)) {}
    };

    HashMap(const InitializerList<InitEntry>& list): capacity(next_power_of_two(list.size())) {
        table = static_cast<Pair*>(malloc(capacity * sizeof(Pair)));
        memset(table, 0, capacity * sizeof(Pair));
        for (u32 i = 0; i < capacity; i++) {
            table[i].used = false;
        }
        for (const InitEntry& entry: list) {
            insert(entry.key, entry.value);
        }
    }

    ~HashMap() {
        for (u32 i = 0; i < capacity; i++) {
            if (table[i].used) {
                table[i].~Pair();
            }
        }
        free(table);
    }

    void insert(const K& k, const V& v) {
        const u32 index = get_insert_index(k);
        if (!table[index].used) {
            length++;
        }
        table[index].set(k, v);
    }

    void insert(const K& k, V&& v) {
        const u32 index = get_insert_index(k);
        if (!table[index].used) {
            length++;
        }
        table[index].set(k, static_cast<V&&>(v));
    }

    void insert(K&& k, const V& v) {
        const u32 index = get_insert_index(k);
        if (!table[index].used) {
            length++;
        }
        table[index].set(static_cast<K&&>(k), v);
    }

    void insert(K&& k, V&& v) {
        const u32 index = get_insert_index(k);
        if (!table[index].used) {
            length++;
        }
        table[index].set(static_cast<K&&>(k), static_cast<V&&>(v));
    }

    template<typename... Args> void emplace(const K& k, Args&&... args) {
        const u32 index = get_insert_index(k);
        if (!table[index].used) {
            length++;
        }
        new (&table[index].key) K(k);
        new (&table[index].value) V(forward<Args>(args)...);
        table[index].used = true;
        table[index].was_used = true;
    }

    template<typename... Args> void emplace(K&& k, Args&&... args) {
        const u32 index = get_insert_index(k);
        if (!table[index].used) {
            length++;
        }
        new (&table[index].key) K(static_cast<K&&>(k));
        new (&table[index].value) V(forward<Args>(args)...);
        table[index].used = true;
        table[index].was_used = true;
    }

    bool remove(const K& k) {
        if (!length) {
            return false;
        }
        const u64 hash = ztd::hash_map_key_hash(k);
        u32 index = hash & (capacity - 1);
        const u32 start = index;
        while (table[index].used) {
            Pair& pair = table[index];
            if (pair.key == k) {
                pair.~Pair();
                length--;
                return true;
            }
            index = probe(index);
            if (index == start) {
                break;
            }
        }
        return false;
    }

    void clear() {
        for (u32 i = 0; i < capacity; i++) {
            if (table[i].used) {
                table[i].~Pair();
            }
        }
        length = 0;
    }

    V* get(const K& k) {
        if (!length) {
            return nullptr;
        }
        const u64 hash = ztd::hash_map_key_hash(k);
        u32 index = hash & (capacity - 1);
        const u32 start = index;
        while (table[index].was_used) {
            if (table[index].used && table[index].key == k) {
                return &table[index].value;
            }
            index = probe(index);
            if (index == start) {
                break;
            }
        }
        return nullptr;
    }

    bool contains(const K& k) {
        return get(k) != nullptr;
    }

    inline u32 len() const {
        return length;
    }

    inline u32 cap() const {
        return capacity;
    }

    inline bool empty() const {
        return length == 0;
    }
};

template<u32 S> struct Str {
    char data[S];

    consteval Str(const char (&value)[S]) {
        for (u32 i = 0; i < S; i++) {
            data[i] = value[i] ^ XOR_KEY[i % 512];
        }
    }

    ~Str() = default;

    FINLINE constexpr u32 len() const {
        return S - 1;
    }

    FINLINE constexpr const char operator[](const u32 index) const {
        return data[index] ^ XOR_KEY[index % 512];
    }
};

template<u32 S> struct StackStr {
    static constexpr u8 CAPACITY = S - 1;
    static constexpr u8 MAX_LENGTH = CAPACITY - 1;

    u8 length = 0;
    char data[CAPACITY] = {0};

    StackStr() = default;

    template<u8 SS> consteval StackStr(const char (&str)[SS]) {
        length = SS - 1;
        if (length > MAX_LENGTH) {
            length = MAX_LENGTH;
        }
        for (u8 i = 0; i < length; i++) {
            data[i] = str[i] ^ XOR_KEY[i % 512];
        }
        data[length] = 0 ^ XOR_KEY[length % 512];
    }

    FINLINE static StackStr<S> format(const struct StrView& format, ...);

    template<u32 SS> FINLINE bool operator==(const StackStr<SS>& str) const {
        if (length != str.length) {
            return false;
        }
        for (u32 i = 0; i < length; i++) {
            if (data[i] != str.data[i]) {
                return false;
            }
        }
        return true;
    }

    FINLINE bool starts_with(const StrView& str) const;
    FINLINE bool contains(const StrView& str) const;
    FINLINE bool ends_with(const StrView& str) const;
    FINLINE bool operator==(const StrView& str) const;

    FINLINE static StackStr<S> from(const char* str) {
        StackStr<S> result = {};
        result.length = u8(strlen(str));
        if (result.length > MAX_LENGTH) {
            result.length = MAX_LENGTH;
        }
        for (u8 i = 0; i < result.length; i++) {
            result.data[i] = str[i] ^ XOR_KEY[i % 512];
        }
        result.data[result.length] = 0 ^ XOR_KEY[result.length % 512];
        return result;
    }

    ~StackStr() = default;

    FINLINE constexpr u8 len() const {
        return length;
    }

    FINLINE constexpr bool empty() const {
        return length == 0;
    }

    FINLINE constexpr const char operator[](const u32 index) const {
        if (index >= length) {
            return 0;
        }
        return data[index] ^ XOR_KEY[index % 512];
    }

    StackStr<S> operator+(const StackStr<S>& str) {
        StackStr<S> result = {};
        result.length = length;
        memcpy(result.data, data, length + 1);
        if (str.length == 0) {
            return result;
        }
        for (u32 i = 0; i < str.length; i++) {
            if (result.length >= MAX_LENGTH) {
                break;
            }
            result.data[result.length++] = str[i] ^ XOR_KEY[length % 512];
        }
        result.data[result.length] = 0 ^ XOR_KEY[result.length % 512];
        return result;
    }

    constexpr const char* toggle() const {
        char* _data = const_cast<char*>(data);
        for (u32 i = 0; i < length; i++) {
            _data[i] ^= XOR_KEY[i % 512];
        }
        _data[length] ^= XOR_KEY[length % 512];
        return _data;
    }
};

using SmallStr = StackStr<128>;
using SmallStr32 = StackStr<32>;
using SmallStr64 = StackStr<64>;

#define SmallStrIM(value)                      \
    []() {                                     \
        static constexpr SmallStr str = value; \
        return str;                            \
    }()

struct StrView {
    char* data;
    u32 length;

    template<u32 S> NOINLINE StrView(const Str<S>& str): data(const_cast<char*>(&str.data[0])), length(str.len()) {}

    template<u32 S> FINLINE StrView(const StackStr<S>& str): data(const_cast<char*>(&str.data[0])), length(str.len()) {}

    FINLINE StrView(const StrView& other): data(other.data), length(other.length) {}

    FINLINE StrView(StrView&& other): data(other.data), length(other.length) {
        other.data = nullptr;
        other.length = 0;
    }

    FINLINE const char operator[](const u32 index) const {
        return data[index] ^ XOR_KEY[index % 512];
    }

    FINLINE bool operator==(const StrView& other) const {
        if (length != other.length) {
            return false;
        }
        for (u32 i = 0; i < length; i++) {
            if (data[i] != other.data[i]) {
                return false;
            }
        }
        return true;
    }

    FINLINE const char* toggle() {
        for (u32 i = 0; i < length; i++) {
            data[i] ^= XOR_KEY[i % 512];
        }
        data[length] ^= XOR_KEY[length % 512];
        return const_cast<char*>(data);
    }
};

template<u32 S> FINLINE bool StackStr<S>::operator==(const StrView& str) const {
    if (length != str.length) {
        return false;
    }
    for (u32 i = 0; i < length; i++) {
        if (data[i] != str.data[i]) {
            return false;
        }
    }
    return true;
}

template<u32 S> FINLINE bool StackStr<S>::starts_with(const StrView& str) const {
    if (str.length == 0) {
        return true;
    }
    if (length == 0) {
        return false;
    }
    if (str.length > length) {
        return false;
    }
    for (u32 i = 0; i < str.length; i++) {
        if (data[i] != str.data[i]) {
            return false;
        }
    }
    return true;
}

template<u32 S> FINLINE bool StackStr<S>::contains(const StrView& str) const {
    if (str.length == 0) {
        return true;
    }
    if (length == 0) {
        return false;
    }
    if (str.length > length) {
        return false;
    }
    for (u32 i = 0; i <= length - str.length; i++) {
        bool found = true;
        for (u32 j = 0; j < str.length; j++) {
            if (this->operator[](i + j) != str[j]) {
                found = false;
                break;
            }
        }
        if (found) {
            return true;
        }
    }
    return false;
}

template<u32 S> FINLINE bool StackStr<S>::ends_with(const StrView& str) const {
    if (str.length == 0) {
        return true;
    }
    if (length == 0) {
        return false;
    }
    if (str.length > length) {
        return false;
    }
    const u32 j = length - str.length;
    for (u32 i = 0; i < str.length; i++) {
        if (this->operator[](j + i) != str[i]) {
            return false;
        }
    }
    return true;
}

template<u32 S> FINLINE StackStr<S> StackStr<S>::format(const StrView& format, ...) {
    FN(u32, "core:format", char*, const StrView&, va_list);
    StackStr<S> result = {};
    va_list args;
    va_start(args, format);
    const u32 n = fn(result.data, format, args);
    va_end(args);
    result.data[n] = 0;
    result.toggle();
    return result;
}

#undef min
#undef max

FINLINE static f32 floor(f32 x) {
    return __builtin_elementwise_floor(x);
}

FINLINE static f32 ceil(f32 x) {
    return __builtin_elementwise_ceil(x);
}

FINLINE static f32 round(f32 x) {
    return __builtin_elementwise_roundeven(x);
}

template<typename T> FINLINE static T abs(T x) {
    return __builtin_elementwise_abs(x);
}

template<typename T> FINLINE static T min(const T a, const T b) {
    return __builtin_elementwise_min(a, b);
}

template<typename T> FINLINE static T max(const T a, const T b) {
    return __builtin_elementwise_max(a, b);
}

STATIC_FUNCTION(f32, sin, f32 value, value, "core:math:sin");
STATIC_FUNCTION(f32, asin, f32 value, value, "core:math:asin");
STATIC_FUNCTION(f32, cos, f32 value, value, "core:math:cos");
STATIC_FUNCTION(f32, acos, f32 value, value, "core:math:acos");
STATIC_FUNCTION(f32, tan, f32 value, value, "core:math:tan");
STATIC_FUNCTION(f32, atan, f32 value, value, "core:math:atan");
STATIC_FUNCTION(f32, atan2, COMBINE(f32 y, f32 x), COMBINE(y, x), "core:math:atan2");
STATIC_FUNCTION(f32, pow, COMBINE(f32 base, f32 exponent), COMBINE(base, exponent), "core:math:pow");
STATIC_FUNCTION(f32, sqrt, f32 value, value, "core:math:sqrt");

struct SpellHash {
   private:

    u32 hash = 0;

   public:

    constexpr SpellHash(u32 hash): hash(hash) {}

    template<u32 S> consteval SpellHash(const char (&value)[S]): hash(fnv1a32_low<S>(value)) {}

    inline operator u32() const {
        return hash;
    }

    inline bool operator==(const SpellHash& other) const {
        return hash == other.hash;
    }
};

template<u32 S> consteval u32 sdbm_low(const char (&value)[S]) {
    u32 hash = 0;
    for (u32 i = 0; i < S - 1; i++) {
        hash = ASCII_LOWER[static_cast<u8>(value[i])] + (hash << 6) + (hash << 16) - hash;
    }
    return hash;
}

struct CharacterHash {
   private:

    u32 hash = 0;

   public:

    constexpr CharacterHash(u32 hash): hash(hash) {}

    template<u32 S> consteval CharacterHash(const char (&value)[S]): hash(sdbm_low<S>(value)) {}

    inline operator u32() const {
        return hash;
    }

    inline bool operator==(const CharacterHash& other) const {
        return hash == other.hash;
    }
};

enum class Team : u8 {
    Unknown = 0,
    Order = 1,
    Chaos = 2,
    Neutral = 3,
};

enum class CombatType : u32 {
    Unknown = 0,
    Melee = 1,
    Ranged = 2,
};

enum class SpellSlot : i32 {
    Unknown = -1,
    Q = 0,
    W = 1,
    E = 2,
    R = 3,
    Summoner1 = 4,
    Summoner2 = 5,
    Item1 = 6,
    Item2 = 7,
    Item3 = 8,
    Item4 = 9,
    Item5 = 10,
    Item6 = 11,
    Trinket = 12,
    Recall = 13,
    Passive = 63,
};

enum class BuffType : i8 {
    Unknown = -1,
    Internal = 0,
    Aura = 1,
    CombatEnchancer = 2,
    CombatDehancer = 3,
    SpellShield = 4,
    Stun = 5,
    Invisibility = 6,
    Silence = 7,
    Taunt = 8,
    Berserk = 9,
    Polymorph = 10,
    Slow = 11,
    Snare = 12,
    Damage = 13,
    Heal = 14,
    Haste = 15,
    SpellImmunity = 16,
    PhysicalImmunity = 17,
    Invulnerability = 18,
    AttackSpeedSlow = 19,
    NearSight = 20,
    Fear = 22,
    Charm = 23,
    Poison = 24,
    Suppression = 25,
    Blind = 26,
    Counter = 27,
    Currency = 21,
    Shred = 28,
    Flee = 29,
    Knockup = 30,
    Knockback = 31,
    Disarm = 32,
    Grounded = 33,
    Drowsy = 34,
    Asleep = 35,
    Obscured = 36,
    ClickproofToEnemies = 37,
    UnKillable = 38,
};

enum class ItemID : u32 {
    Unknown = 0,
    AbyssalMask = 8020,
    AbyssalMask2 = 328020,
    AbyssalMaskArena = 228020,
    AdaptiveHelmArena = 228004,
    AegisOfTheLegion = 3105,
    AetherWisp = 3113,
    AmplifyingTome = 1052,
    AnathemasChains = 8001,
    AnathemasChainsArena = 228001,
    AntiTowerSocks = 1508,
    AnvilVoucherArena = 220008,
    ArcaneSweeper = 3348,
    ArchangelsStaff = 3003,
    ArchangelsStaff2 = 323003,
    ArchangelsStaffArena = 223003,
    ArdentCenser = 3504,
    ArdentCenser2 = 323504,
    ArdentCenserArena = 223504,
    ArmoredAdvance = 3174,
    AtmasReckoningArena = 223039,
    AxiomArc = 6696,
    AxiomArcArena = 226696,
    BFSword = 1038,
    BamisCinder = 6660,
    BandleglassMirror = 4642,
    BansheesVeil = 3102,
    BansheesVeilArena = 223102,
    BerserkersGreaves = 3006,
    BerserkersGreavesArena = 223006,
    BlackCleaver = 3071,
    BlackCleaverArena = 223071,
    BlackHoleGauntletArena = 447122,
    BlackfireTorch = 2503,
    BlackfireTorchArena = 222503,
    BladeOfTheRuinedKing = 3153,
    BladeOfTheRuinedKingArena = 223153,
    BlastingWand = 1026,
    BlastingWandArena = 221026,
    BlightingJewel = 4630,
    BloodlettersCurse = 4010,
    BloodlettersCurse2 = 8010,
    Bloodsong = 3877,
    Bloodthirster = 3072,
    BloodthirsterArena = 223072,
    Boots = 1001,
    BootsOfSwiftness = 3009,
    BootsOfSwiftnessArena = 223009,
    BountyOfWorlds = 3867,
    BrambleVest = 3076,
    BraveryVoucherArena = 220011,
    CappaJuice = 2141,
    CappaJuiceArena = 222141,
    CatalystOfAeons = 3803,
    CaulfieldsWarhammer = 3133,
    CelestialOpposition = 3869,
    ChainVest = 1031,
    ChainlacedCrushers = 3173,
    ChempunkChainsword = 6609,
    ChempunkChainswordArena = 226609,
    ChemtechPutrifier = 3011,
    ChemtechPutrifierArena = 223011,
    CloakOfAgility = 1018,
    CloakOfStarryNightArena = 443059,
    ClothArmor = 1029,
    ControlWard = 2055,
    CorruptingPotion = 2033,
    CosmicDrive = 4629,
    CosmicDriveArena = 224629,
    CrimsonLucidity = 3171,
    CrownOfTheShatteredQueenArena = 444644,
    CrueltyArena = 447109,
    Cryptbloom = 3137,
    CryptbloomArena = 223137,
    CrystallineBracer = 3801,
    Cull = 1083,
    Dagger = 1042,
    DarkSeal = 1082,
    DarksteelTalonsArena = 443054,
    Dawncore = 6621,
    Dawncore2 = 326621,
    DawncoreArena = 226621,
    DeadMansPlate = 3742,
    DeadMansPlateArena = 223742,
    DeathbladeArena = 228003,
    DeathsDance = 6333,
    DeathsDanceArena = 226333,
    DeathsDaughter = 3902,
    DecapitatorArena = 447107,
    DemonKingsCrownArena = 443056,
    DemonicEmbrace = 4637,
    DemonicEmbraceArena = 444637,
    DetonationOrbArena = 447113,
    DiamondTippedSpearArena = 447120,
    DivineSundererArena = 446632,
    DoransBlade = 1055,
    DoransRing = 1056,
    DoransShield = 1054,
    DragonheartArena = 447106,
    DreamMaker = 3870,
    DuskbladeOfDraktharrArena = 446691,
    EchoesOfHelia = 6620,
    EchoesOfHelia2 = 326620,
    EchoesOfHeliaArena = 226620,
    Eclipse = 6692,
    EclipseArena = 226692,
    EdgeOfNight = 3814,
    EdgeOfNightArena = 223814,
    EleisasMiracleArena = 443063,
    ElixirOfAvarice = 2151,
    ElixirOfForce = 2152,
    ElixirOfIron = 2138,
    ElixirOfSkill = 2150,
    ElixirOfSorcery = 2139,
    ElixirOfWrath = 2140,
    EmpyreanPromiseArena = 447105,
    EnhancedLuckyDice = 2146,
    EssenceReaver = 3508,
    EssenceReaverArena = 223508,
    EverfrostArena = 446656,
    ExecutionersCalling = 3123,
    ExperimentalHexplate = 3073,
    ExperimentalHexplateArena = 223073,
    FaerieCharm = 1004,
    FarsightAlteration = 3363,
    FatedAshes = 2508,
    FiendishCodex = 3108,
    Fimbulwinter = 3121,
    Fimbulwinter2 = 323121,
    FimbulwinterArena = 223121,
    FireAtWill = 3901,
    FlesheaterArena = 447112,
    ForbiddenIdol = 3114,
    ForceOfEntropyArena = 443061,
    ForceOfNature = 4401,
    ForceOfNatureArena = 224401,
    ForeverForward = 3176,
    Fortification = 1501,
    Fortification2 = 1521,
    FrozenHeart = 3110,
    FrozenHeart2 = 323110,
    FrozenHeartArena = 223110,
    FulminationArena = 443055,
    GaleforceArena = 446671,
    GamblersBladeArena = 447101,
    GangplankPlaceholder = 7050,
    GargoyleStoneplateArena = 443193,
    GhostcrawlersArena = 223005,
    GiantsBelt = 1011,
    GlacialBuckler = 3024,
    GlowingMote = 2022,
    GoldStatAnvilVoucherArena = 220009,
    GoredrinkerArena = 226630,
    GuardianAngel = 3026,
    GuardianAngelArena = 223026,
    GuardiansAmulet = 2049,
    GuardiansBladeArena = 223177,
    GuardiansDirkArena = 223185,
    GuardiansHammerArena = 223184,
    GuardiansHornArena = 222051,
    GuardiansOrbArena = 223112,
    GuardiansShroud = 2050,
    GuinsoosRageblade = 3124,
    GuinsoosRagebladeArena = 223124,
    GunmetalGreaves = 3172,
    Gusto = 1509,
    GustwalkerHatchling = 1102,
    GustwalkerHatchling2 = 1106,
    HamstringerArena = 443069,
    HauntingGuise = 3147,
    HealthPotion = 2003,
    HearthboundAxe = 3051,
    Heartsteel = 3084,
    HeartsteelArena = 223084,
    HellfireHatchet = 4017,
    HemomancersHelmArena = 447103,
    HexboltCompanionArena = 443081,
    Hexdrinker = 3155,
    HextechAlternator = 3145,
    HextechGunbladeArena = 223146,
    HextechRocketbelt = 3152,
    HextechRocketbeltArena = 223152,
    HollowRadiance = 6664,
    HollowRadianceArena = 226664,
    HorizonFocus = 4628,
    HorizonFocusArena = 224628,
    Hubris = 6697,
    HubrisArena = 226697,
    Hullbreaker = 3181,
    HullbreakerArena = 223181,
    IcebornGauntlet = 6662,
    IcebornGauntletArena = 226662,
    ImmortalShieldbow = 6673,
    ImmortalShieldbowArena = 226673,
    ImperialMandate = 4005,
    ImperialMandate2 = 324005,
    ImperialMandateArena = 224005,
    InfinityEdge = 3031,
    InfinityEdgeArena = 223031,
    InnervatingLocketArena = 447104,
    IonianBootsOfLucidity = 3158,
    IonianBootsOfLucidityArena = 223158,
    JakshoTheProtean = 6665,
    JakshoTheProteanArena = 226665,
    JuiceOfHaste = 2144,
    JuiceOfPower = 2142,
    JuiceOfVitality = 2143,
    KaenicRookern = 2504,
    KaenicRookernArena = 222504,
    KalistasBlackSpear = 3599,
    KalistasBlackSpear2 = 3600,
    Kindlegem = 3067,
    KinkouJitteArena = 447116,
    KnightsVow = 3109,
    KnightsVow2 = 323109,
    KnightsVowArena = 223109,
    KrakenSlayer = 6672,
    KrakenSlayerArena = 226672,
    LastWhisper = 3035,
    LeechingLeer = 4635,
    LegendaryAssassinItemArena = 220003,
    LegendaryFighterItemArena = 220001,
    LegendaryMageItemArena = 220004,
    LegendaryMarksmanItemArena = 220002,
    LegendarySupportItemArena = 220006,
    LegendaryTankItemArena = 220005,
    LiandrysAnguishArena = 226653,
    LiandrysTorment = 6653,
    LichBane = 3100,
    LichBaneArena = 223100,
    LightningRodArena = 447119,
    LocketOfTheIronSolari = 3190,
    LocketOfTheIronSolari2 = 323190,
    LocketOfTheIronSolariArena = 223190,
    LongSword = 1036,
    LordDominiksRegards = 3036,
    LordDominiksRegardsArena = 223036,
    LostChapter = 3802,
    LuckyDice = 2145,
    LudensCompanion = 6655,
    LudensCompanionArena = 226655,
    Malignance = 3118,
    MalignanceArena = 223118,
    Manamune = 3004,
    Manamune2 = 323004,
    ManamuneArena = 223004,
    MawOfMalmortius = 3156,
    MawOfMalmortiusArena = 223156,
    MejaisSoulstealer = 3041,
    MercurialScimitar = 3139,
    MercurialScimitarArena = 223139,
    MercurysTreads = 3111,
    MercurysTreadsArena = 223111,
    MikaelsBlessing = 3222,
    MikaelsBlessing2 = 323222,
    MikaelsBlessingArena = 223222,
    MinionDematerializer = 2403,
    MirageBladeArena = 447100,
    MobilityBoots = 3117,
    MoonflairSpellbladeArena = 447110,
    MoonstoneRenewer = 6617,
    MoonstoneRenewer2 = 326617,
    MoonstoneRenewerArena = 226617,
    Morellonomicon = 3165,
    MorellonomiconArena = 223165,
    MortalReminder = 3033,
    MortalReminderArena = 223033,
    MosstomperSeedling = 1103,
    MosstomperSeedling2 = 1105,
    Muramana = 3042,
    Muramana2 = 323042,
    MuramanaArena = 223042,
    NashorsTooth = 3115,
    NashorsToothArena = 223115,
    NavoriFlickerblade = 6675,
    NavoriFlickerbladesArena = 226675,
    NeedlesslyLargeRod = 1058,
    NeedlesslyLargeRodArena = 221058,
    NegatronCloak = 1057,
    NightHarvester = 4636,
    NightHarvesterArena = 444636,
    Noonquiver = 6670,
    NullMagicMantle = 1033,
    OblivionOrb = 3916,
    ObsidianCleaverArena = 228005,
    Opportunity = 6701,
    OpportunityArena = 226701,
    OracleLens = 3364,
    Overcharged = 1507,
    Overerchargedha = 1520,
    OverlordsBloodmail = 2501,
    OverlordsBloodmailArena = 447111,
    PenetratingBullets = 1500,
    Perplexity = 4015,
    Phage = 3044,
    PhantomDancer = 3046,
    PhantomDancerArena = 223046,
    PhreakishGusto = 1510,
    Pickaxe = 1037,
    PlatedSteelcaps = 3047,
    PlatedSteelcapsArena = 223047,
    PoroSnax = 2052,
    PrismaticItemArena = 220007,
    PrismaticStatVoucherArena = 220010,
    ProfaneHydra = 6698,
    ProfaneHydraArena = 226698,
    ProwlersClaw = 6693,
    ProwlersClawArena = 226693,
    PuppeteerArena = 447123,
    PyromancersCloakArena = 447118,
    QuicksilverSash = 3140,
    RabadonsDeathcap = 3089,
    RabadonsDeathcapArena = 223089,
    RadiantVirtueArena = 446667,
    RaiseMorale = 3903,
    RanduinsOmen = 3143,
    RanduinsOmenArena = 223143,
    RapidFirecannon = 3094,
    RapidFirecannonArena = 223094,
    RavenousHydra = 3074,
    RavenousHydraArena = 223074,
    RealityFractureArena = 447102,
    ReapersTollArena = 443090,
    Rectrix = 6690,
    RecurveBow = 1043,
    Redemption = 3107,
    Redemption2 = 323107,
    RedemptionArena = 223107,
    RefillablePotion = 2031,
    RegicideArena = 447115,
    ReinforcedArmor = 1502,
    ReinforcedArmor2 = 1506,
    RejuvenationBead = 1006,
    ReverberationArena = 447114,
    Riftmaker = 4633,
    RiftmakerArena = 224633,
    RiteOfRuin = 3430,
    RodOfAges = 6657,
    RodOfAges2 = 326657,
    RodOfAgesArena = 226657,
    RubyCrystal = 1028,
    RunaansHurricane = 3085,
    RunaansHurricaneArena = 223085,
    RunecarverArena = 447108,
    RuneglaiveArena = 228008,
    RunicCompass = 3866,
    RylaisCrystalScepter = 3116,
    RylaisCrystalScepterArena = 223116,
    SanguineBladeArena = 228006,
    SanguineGiftArena = 443062,
    SapphireCrystal = 1027,
    ScarecrowEffigy = 3330,
    ScorchclawPup = 1101,
    ScorchclawPup2 = 1107,
    ScoutsSlingshot = 3144,
    SeekersArmguard = 2420,
    SeraphsEmbrace = 3040,
    SeraphsEmbrace2 = 323040,
    SeraphsEmbraceArena = 223040,
    SerpentsFang = 6695,
    SerpentsFangArena = 226695,
    SerratedDirk = 3134,
    SeryldasGrudge = 6694,
    SeryldasGrudgeArena = 226694,
    Shadowflame = 4645,
    ShadowflameArena = 224645,
    ShatteredArmguard = 2421,
    Sheen = 3057,
    ShieldOfMoltenStoneArena = 443058,
    ShurelyasBattlesong = 2065,
    ShurelyasBattlesong2 = 322065,
    ShurelyasBattlesongArena = 222065,
    SlightlyMagicalFootwear = 2422,
    SolsticeSleigh = 3876,
    SorcerersShoes = 3020,
    SorcerersShoesArena = 223020,
    SpearOfShojin = 3161,
    SpearOfShojinArena = 223161,
    SpectralCutlassArena = 224004,
    SpectresCowl = 3211,
    SpellslingersShoes = 3175,
    SpiritVisage = 3065,
    SpiritVisageArena = 223065,
    StaffOfFlowingWater = 6616,
    StaffOfFlowingWater2 = 326616,
    StaffOfFlowingWaterArena = 226616,
    StatBonusArena = 220000,
    StatikkShiv = 3087,
    StatikkShivArena = 223087,
    StealthWard = 3340,
    SteelSigil = 2019,
    SteraksGage = 3053,
    SteraksGageArena = 223053,
    StirringWardstone = 4641,
    StormrazorArena = 223095,
    Stormsurge = 4646,
    StormsurgeArena = 224646,
    Stridebreaker = 6631,
    StridebreakerArena = 226631,
    StructureBounty = 1516,
    StructureBounty2 = 1517,
    StructureBounty3 = 1518,
    StructureBounty4 = 1519,
    SunderedSky = 6610,
    SunderedSkyArena = 226610,
    SunfireAegis = 3068,
    SunfireAegisArena = 223068,
    SuperMechArmor = 1511,
    SuperMechPowerField = 1512,
    Swiftmarch = 3170,
    SwordOfBlossomingDawn = 4011,
    SwordOfTheDivineArena = 443060,
    SymbioticSoles = 3010,
    SynchronizedSouls = 3013,
    TalismanOfAscensionArena = 443064,
    TearOfTheGoddess = 3070,
    TearOfTheGoddess2 = 323070,
    Terminus = 3302,
    TerminusArena = 223302,
    TheBrutalizer = 2020,
    TheCollector = 6676,
    TheCollectorArena = 226676,
    TheGoldenSpatulaArena = 224403,
    Thornmail = 3075,
    Thornmail2 = 323075,
    ThornmailArena = 223075,
    Tiamat = 3077,
    TitanicHydra = 3748,
    TitanicHydraArena = 223748,
    TotalBiscuitOfEverlastingWill = 2010,
    TowerPowerUp = 1522,
    Trailblazer = 3002,
    Trailblazer2 = 323002,
    TrinityForce = 3078,
    TrinityForceArena = 223078,
    Tunneler = 2021,
    TurboChemtankArena = 443079,
    TurretPlating = 1515,
    TwilightsEdgeArena = 447121,
    TwinMaskArena = 443080,
    UmbralGlaive = 3179,
    UnendingDespair = 2502,
    UnendingDespairArena = 222502,
    VampiricScepter = 1053,
    VerdantBarrier = 4632,
    VigilantWardstone = 4643,
    VoidStaff = 3135,
    VoidStaffArena = 223135,
    VoltaicCyclosword = 6699,
    VoltaicCycloswordArena = 226699,
    WardensEye = 1503,
    WardensMail = 3082,
    WarmogsArmor = 3083,
    WarmogsArmorArena = 443083,
    WatchfulWardstone = 4638,
    WingedMoonplate = 3066,
    WintersApproach = 3119,
    WintersApproach2 = 323119,
    WintersApproachArena = 223119,
    WitsEnd = 3091,
    WitsEndArena = 223091,
    WoogletsWitchcapArena = 228002,
    WordlessPromise = 4016,
    WorldAtlas = 3865,
    YoumuusGhostblade = 3142,
    YoumuusGhostbladeArena = 223142,
    YourCut = 3400,
    YunTalWildarrows = 3032,
    YunTalWildarrowsArena = 223032,
    ZazzaksRealmspike = 3871,
    Zeal = 3086,
    ZekesConvergence = 3050,
    ZekesConvergence2 = 323050,
    ZekesConvergenceArena = 223050,
    ZephyrArena = 223172,
    ZhonyasHourglass = 3157,
    ZhonyasHourglassArena = 223157,
};

struct ActionMode {
    enum Value : u8 {
        None = 0,
        Combo = 1 << 0,
        Harass = 1 << 1,
        Farm = 1 << 2,
        LastHit = 1 << 3,
        Flee = 1 << 4,
        FastFarm = 1 << 5,
    };

    ActionMode() = default;

    constexpr ActionMode(Value value): value(value) {}

    inline void add(const Value state) {
        value = static_cast<Value>(static_cast<u8>(value) | static_cast<u8>(state));
    }

    inline bool eq(const Value state) const {
        return value == state;
    }

    inline bool is(const Value state) const {
        return (static_cast<u8>(value) & static_cast<u8>(state)) != 0;
    }

    inline bool operator==(const Value state) const {
        return value == state || is(state);
    }

   private:

    Value value = Value::None;
};

struct CollisionFlags {
    enum Value : u16 {
        None = 0,
        Grass = 1 << 0,
        Wall = 1 << 1,
        Building = 1 << 6,
        Prop = 1 << 7,
        GlobalVision = 1 << 8,
    };

    constexpr CollisionFlags() = default;

    constexpr CollisionFlags(Value value): value(value) {}

    FINLINE bool is_wall() const {
        return value & Wall;
    }

    FINLINE bool is_grass() const {
        return value & Grass;
    }

    FINLINE bool is_building() const {
        return value & Building;
    }

    FINLINE bool is_prop() const {
        return value & Prop;
    }

    FINLINE bool is_global_vision() const {
        return value & GlobalVision;
    }

    FINLINE operator u16() const {
        return value;
    }

   private:

    Value value;
};

enum class TargetMode : u8 {
    LessAttacks,
    LowestHealth,
    ClosestToHero,
    ClosestToMouse,
    Default = 255,
};

enum class DamageMode : u8 {
    AD,
    AP,
    AA,
    Adaptive
};

struct Vec2 {
    f32 x = 0.0f;
    f32 y = 0.0f;

    Vec2() = default;

    Vec2(f32 x, f32 y): x(x), y(y) {}
};

struct Position {
    f32 x = 0.0f;
    f32 height = 0.0f;
    f32 y = 0.0f;

    constexpr Position() = default;

    constexpr Position(f32 x, f32 y, f32 height): x(x), height(height), y(y) {}

    FINLINE bool empty() const {
        return x == 0.0f && y == 0.0f && height == 0.0f;
    }

    FINLINE void reset() {
        x = 0.0f;
        y = 0.0f;
        height = 0.0f;
    }

    FINLINE bool is_valid() const {
        return x > 0.0f && y > 0.0f;
    }

    FINLINE f32 len_sqrd() const {
        return x * x + y * y;
    }

    FINLINE f32 len() const {
        return sqrt(len_sqrd());
    }

    FINLINE f32 distance_sqrd(const Position& to) const {
        f32 _x = x - to.x;
        f32 _y = y - to.y;
        return _x * _x + _y * _y;
    }

    FINLINE f32 distance(const Position& to) const {
        return sqrt(distance_sqrd(to));
    }

    FINLINE Position between(const Position& other) const {
        return Position((x + other.x) * 0.5f, (y + other.y) * 0.5f, (height + other.height) * 0.5f);
    }

    FINLINE Position extend(const Position& to, f32 extend_distance) const {
        if (extend_distance == 0.0f) {
            return Position(x, y, height);
        }
        f32 _distance = distance(to);
        if (_distance == 0.0f) {
            return Position(x, y, height);
        }
        f32 rate = extend_distance / _distance;
        return Position(x + (to.x - x) * rate, y + (to.y - y) * rate, height);
    }

    FINLINE Position rotate(const Position& center, f32 angle) const {
        const f32 cos_a = cos(angle);
        const f32 sin_a = sin(angle);
        const f32 _x = x - center.x;
        const f32 _y = y - center.y;
        return Position(center.x + _x * cos_a - _y * sin_a, center.y + _x * sin_a + _y * cos_a, height);
    }

    FINLINE bool operator==(const Position& other) const {
        return distance_sqrd(other) < FLT_EPSILON;
    }

    FINLINE f32 dot(const Position& other) const {
        return x * other.x + y * other.y;
    }

    FINLINE Position operator+(const Position& other) const {
        return Position(x + other.x, y + other.y, height + other.height);
    }

    FINLINE Position operator-(const Position& other) const {
        return Position(x - other.x, y - other.y, height - other.height);
    }

    FINLINE Position operator*(f32 other) const {
        return Position(x * other, y * other, height * other);
    }

    FINLINE Position normalized() const {
        const f32 len = sqrt(x * x + y * y);
        if (len != 0.0f) {
            const f32 inv = 1.0f / len;
            return Position(x * inv, y * inv, height);
        }
        return *this;
    }
};

struct Color {
    f32 r = 0.0f;
    f32 g = 0.0f;
    f32 b = 0.0f;
    f32 a = 1.0f;

    constexpr Color(u8 r, u8 g, u8 b) {
        constexpr f32 sc = 1.0f / 255.0f;
        this->r = f32(r) * sc;
        this->g = f32(g) * sc;
        this->b = f32(b) * sc;
    }

    constexpr Color(u8 r, u8 g, u8 b, u8 a) {
        constexpr f32 sc = 1.0f / 255.0f;
        this->r = f32(r) * sc;
        this->g = f32(g) * sc;
        this->b = f32(b) * sc;
        this->a = f32(a) * sc;
    }
};

constexpr Color COLOR_BLACK = Color(0, 0, 0);
constexpr Color COLOR_WHITE = Color(255, 255, 255);
constexpr Color COLOR_RED = Color(255, 0, 0);
constexpr Color COLOR_GREEN = Color(0, 255, 0);
constexpr Color COLOR_BLUE = Color(0, 0, 255);

struct Circle {
    Position center = {};
    f32 radius = 0.0f;

    constexpr Circle() = default;

    constexpr Circle(const Position& center, f32 radius): center(center), radius(radius) {}

    FINLINE bool inside(const Position& point) const {
        return center.distance_sqrd(point) < radius * radius;
    }

    FINLINE bool intersect(const Circle& other) const {
        const f32 max_distance = radius + other.radius;
        return center.distance_sqrd(other.center) < max_distance * max_distance;
    }
};

struct Rectangle {
    Position start = {};
    Position end = {};
    f32 height = 0.0f;

    constexpr Rectangle() = default;

    constexpr Rectangle(const Position& start, const Position& end, f32 height): start(start), end(end), height(height) {}

    FINLINE bool inside(const Position& point) const {
        const Position dir = end - start;
        const Position point_dir = point - start;
        const f32 len_sqrd = dir.len_sqrd();
        if (len_sqrd == 0.0f) {
            return false;
        }
        const f32 projection = dir.dot(point_dir) / len_sqrd;
        if (projection < 0.0f || projection > 1.0f) {
            return false;
        }
        const Position projection_point = start + dir * projection;
        const f32 perpendicular_dist = (point - projection_point).len();
        if (perpendicular_dist > height) {
            return false;
        }
        return true;
    }

    FINLINE bool intersect(const Circle& circle) const {
        const Position _start = start.extend(end, -circle.radius);
        const Position _end = end.extend(start, -circle.radius);
        const Position dir = _end - _start;
        const Position circle_dir = circle.center - _start;
        const f32 len_sqrd = dir.len_sqrd();
        if (len_sqrd == 0.0f) {
            return false;
        }
        const f32 projection = dir.dot(circle_dir) / len_sqrd;
        if (projection < 0.0f || projection > 1.0f) {
            return false;
        }
        const Position projection_point = _start + dir * projection;
        const f32 perpendicular_dist = (circle.center - projection_point).len();
        if (perpendicular_dist > height + circle.radius) {
            return false;
        }
        return true;
    }

    FINLINE bool intersect(const Rectangle& other) const {
        const Position dir = end - start;
        const Position other_dir = other.end - other.start;
        const f32 len_sqrd = dir.len_sqrd();
        const f32 other_len_sqrd = other_dir.len_sqrd();
        if (len_sqrd == 0.0f || other_len_sqrd == 0.0f) {
            return false;
        }
        const f32 projection = dir.dot(other_dir) / len_sqrd;
        if (projection < 0.0f || projection > 1.0f) {
            return false;
        }
        const Position projection_point = start + dir * projection;
        const f32 perpendicular_dist = (other.start - projection_point).len();
        if (perpendicular_dist > height + other.height) {
            return false;
        }
        return true;
    }
};

#define FIELD(type, name, key)                                             \
    type name() const {                                                    \
        static const auto fn = (type(*)(const void*))(load_function(key)); \
        return fn(this);                                                   \
    }
#define METHOD_0(type, name, key)                                          \
    type name() const {                                                    \
        static const auto fn = (type(*)(const void*))(load_function(key)); \
        return fn(this);                                                   \
    }
#define METHOD(type, name, args, params, key)                                    \
    type name(args) const {                                                      \
        static const auto fn = (type(*)(const void*, args))(load_function(key)); \
        return fn(this, params);                                                 \
    }

struct OnPreSpellLaunchEvent {
    const struct SpellCastInfo* spell_cast_info;
    const struct AIBaseClient* caster;
};

struct OnStartSpellCastEvent {
    const struct SpellCastInfo* spell_cast_info;
    const struct AIBaseClient* caster;
};

struct OnExecuteCastFrameEvent {
    const struct SpellInstanceClient* spell_instance_client;
    const struct AIBaseClient* caster;
};

struct OnSpellImpactEvent {
    const struct SpellCastInfo* spell_cast_info;
    const struct AIBaseClient* caster;
};

struct OnStopCastEvent {
    const struct SpellInstanceClient* spell_instance_client;
    const struct AIBaseClient* caster;
    bool stop_animation;
    bool force_stop;
};

struct OnBuffEvent {
    const struct BuffInstance* buff;
    const struct AIBaseClient* owner;
    bool gain;
};

struct OnPlayAnimationEvent {
    const struct AIBaseClient* owner;
    const char* name;
};

struct OnBeforeAttackEvent {
    const struct AttackableUnit* target;
    bool* prevent;
};

struct OnBeforeMoveEvent {
    Position* position;
    bool* prevent;
};

struct OnValidateAndCastSpellhEvent {
    const struct SpellData* spell_data;
    bool manual;
    bool* prevent;
};

enum class EventKind : i8 {
    OnUpdate,
    OnCreateObject,
    OnDeleteObject,
    OnPreSpellLaunch,
    OnStartSpellCast,
    OnExecuteCastFrame,
    OnSpellImpact,
    OnStopCast,
    OnBuff,
    OnPlayAnimation,
    OnDrawGround,
    OnDrawHud,
    OnDrawMenu,
    OnBeforeAttack,
    OnBeforeMove,
    OnValidateAndCastSpell,
    OnAfterAttack,
};

namespace renderer {
    enum class Align {
        LeftBottom,
        LeftCenter,
        LeftTop,
        CenterBottom,
        CenterCenter,
        CenterTop,
        RightBottom,
        RightCenter,
        RightTop,
    };

    static Vec2 get_window_size() {
        FN(Vec2, "core:renderer:get_window_size");
        return fn();
    }

    static void draw_circle_2d(const Vec2& center, const f32 radius, const Color& color, const f32 thickness = 0.0f, const f32 aa = 2.0f) {
        FN(void, "core:renderer:draw_circle_2d", const Vec2&, const f32, const Color&, const f32, const f32);
        return fn(center, radius, color, thickness, aa);
    }

    static void draw_rect_2d_fill(const Vec2& position, const Vec2& size, const Color& color, const f32 rounding = 0.0f) {
        FN(void, "core:renderer:draw_rect_2d_fill", const Vec2&, const Vec2&, const Color&, const f32);
        return fn(position, size, color, rounding);
    }

    static void
    draw_rect_2d_stroke(const Vec2& position, const Vec2& size, const Color& color, const f32 thickness = 1.0f, const f32 rounding = 0.0f) {
        FN(void, "core:renderer:draw_rect_2d_stroke", const Vec2&, const Vec2&, const Color&, const f32, const f32);
        return fn(position, size, color, thickness, rounding);
    }

    static void draw_polygon_2d(
        const Vec<Vec2>& points,
        const Color& color,
        const f32 thickness = 1.0f,
        const f32 glow = 0.0f,
        const f32 glow_opacity = 0.35f,
        const f32 background_opacity = 0.0f,
        const bool close = true
    ) {
        FN(void, "core:renderer:draw_polygon_2d", const Vec<Vec2>&, const Color&, const f32, const f32, const f32, const f32, const bool);
        return fn(points, color, thickness, glow, glow_opacity, background_opacity, close);
    }

    static void draw_polyline_2d(
        const Vec<Vec2>& points,
        const Color& color,
        const f32 thickness = 1.0f,
        const f32 glow = 0.0f,
        const f32 glow_opacity = 0.35f
    ) {
        FN(void, "core:renderer:draw_polyline_2d", const Vec<Vec2>&, const Color&, const f32, const f32, const f32);
        return fn(points, color, thickness, glow, glow_opacity);
    }

    static void draw_texture_2d(
        const Vec2& position,
        const Vec2& size,
        void* texture_view,
        const Vec2& uv_min = Vec2(0.0f, 0.0f),
        const Vec2& uv_max = Vec2(1.0f, 1.0f)
    ) {
        FN(void, "core:renderer:draw_texture_2d", const Vec2&, const Vec2&, void*, const Vec2&, const Vec2&);
        return fn(position, size, texture_view, uv_min, uv_max);
    }

    // static void draw_texture_2d_riot(const Vec2& position, const Vec2& size, const R3dTexture* texture) {
    //     static const auto fn =
    //         (void (*)(const Vec2& position, const Vec2& size, const R3dTexture*
    //         texture))(load_function("core:renderer:draw_texture_2d_riot"
    //         ));
    //     return fn(position, size, texture);
    // }

    static void
    draw_text_2d(const f32 font_size, Vec2 position, const Color& color, const StrView& text, const Align align = Align::LeftTop) {
        FN(void, "core:renderer:draw_text_2d", const f32, Vec2, const Color&, const StrView&, const Align);
        return fn(font_size, position, color, text, align);
    }

    static void draw_text_2d_with_shadow(
        const f32 font_size,
        const Vec2& position,
        const Color& color,
        const StrView& text,
        const Align align = Align::LeftTop,
        const Color& shadow_color = COLOR_BLACK
    ) {
        FN(void, "core:renderer:draw_text_2d_with_shadow", f32, const Vec2&, const Color&, const StrView&, Align, const Color&);
        return fn(font_size, position, color, text, align, shadow_color);
    }

    static void draw_circle_3d(
        const Position& center,
        const f32 radius,
        const Color& color,
        const f32 thickness = 1.0f,
        const f32 outer_glow = 0.0f,
        const f32 inner_glow = 0.0f,
        const f32 glow_opacity = 0.35f,
        const f32 background_opacity = 0.0f
    ) {
        FN(void, "core:renderer:draw_circle_3d", const Position&, f32, const Color&, f32, f32, f32, f32, f32);
        return fn(center, radius, color, thickness, outer_glow, inner_glow, glow_opacity, background_opacity);
    }

    static void draw_rect_3d(
        const Position& start,
        const Position& end,
        const f32 height,
        const Color& color,
        const f32 thickness = 1.0f,
        const f32 rounding = 0.0f,
        const f32 outer_glow = 0.0f,
        const f32 inner_glow = 0.0f,
        const f32 glow_opacity = 0.35f
    ) {
        FN(void, "core:renderer:draw_rect_3d", const Position&, const Position&, f32, const Color&, f32, f32, f32, f32, f32);
        return fn(start, end, height, color, thickness, rounding, outer_glow, inner_glow, glow_opacity);
    }

    static void draw_polygon_3d(
        const Vec<Position>& points,
        const Color& color,
        const f32 thickness = 1.0f,
        const f32 glow = 0.0f,
        const f32 glow_opacity = 0.35f,
        const f32 background_opacity = 0.0f,
        const bool close = true
    ) {
        FN(void, "core:renderer:draw_polygon_3d", const Vec<Position>&, const Color&, f32, f32, f32, f32, bool);
        return fn(points, color, thickness, glow, glow_opacity, background_opacity, close);
    }

    static void draw_polyline_3d(
        const Vec<Position>& points,
        const Color& color,
        const f32 thickness = 1.0f,
        const f32 glow = 0.0f,
        const f32 glow_opacity = 0.35f
    ) {
        FN(void, "core:renderer:draw_polyline_3d", const Vec<Position>&, const Color&, f32, f32, f32);
        return fn(points, color, thickness, glow, glow_opacity);
    }
} // namespace renderer

struct MenuPage {
   private:

    u64 value = 0;

   public:

    constexpr MenuPage() = default;

    constexpr MenuPage(const u64 value): value(value) {}

    template<u32 S> consteval MenuPage(const char (&str)[S]): value(fnv1a64<S>(str)) {}

    constexpr operator u64() const {
        return value;
    }

    inline bool operator==(const MenuPage& page) const {
        return page.value == value;
    }
};

#define MENU_PAGE(key, value) static constexpr MenuPage key = value;

struct SettingsKey {
   private:

    u64 value = 0;

   public:

    constexpr SettingsKey() = default;

    consteval SettingsKey(const u64 value): value(value) {}

    template<u32 S> consteval SettingsKey(const char (&str)[S]): value(fnv1a64<S>(str)) {}

    constexpr operator u64() const {
        return value;
    }
};

enum class VirtualKey : u8 {
    None = 0x0,
    LButton = 0x1,
    RButton = 0x2,
    MButton = 0x4,
    XButton1 = 0x5,
    XButton2 = 0x6,
    Back = 0x8,
    Tab = 0x9,
    Shift = 0x10,
    Enter = 0xD,
    Pause = 0x13,
    CapsLock = 0x14,
    Escape = 0x1B,
    Space = 0x20,
    PageUp = 0x21,
    PageDown = 0x22,
    End = 0x23,
    Home = 0x24,
    Left = 0x25,
    Up = 0x26,
    Right = 0x27,
    Down = 0x28,
    Print = 0x2A,
    PrintScreen = 0x2C,
    Insert = 0x2D,
    Delete = 0x2E,
    D0 = 0x30,
    D1 = 0x31,
    D2 = 0x32,
    D3 = 0x33,
    D4 = 0x34,
    D5 = 0x35,
    D6 = 0x36,
    D7 = 0x37,
    D8 = 0x38,
    D9 = 0x39,
    A = 0x41,
    B = 0x42,
    C = 0x43,
    D = 0x44,
    E = 0x45,
    F = 0x46,
    G = 0x47,
    H = 0x48,
    I = 0x49,
    J = 0x4A,
    K = 0x4B,
    L = 0x4C,
    M = 0x4D,
    N = 0x4E,
    O = 0x4F,
    P = 0x50,
    Q = 0x51,
    R = 0x52,
    S = 0x53,
    T = 0x54,
    U = 0x55,
    V = 0x56,
    W = 0x57,
    X = 0x58,
    Y = 0x59,
    Z = 0x5A,
    LWin = 0x5B,
    RWin = 0x5C,
    NumPad0 = 0x60,
    NumPad1 = 0x61,
    NumPad2 = 0x62,
    NumPad3 = 0x63,
    NumPad4 = 0x64,
    NumPad5 = 0x65,
    NumPad6 = 0x66,
    NumPad7 = 0x67,
    NumPad8 = 0x68,
    NumPad9 = 0x69,
    Multiply = 0x6A,
    Add = 0x6B,
    Subtract = 0x6D,
    Decimal = 0x6E,
    Divide = 0x6F,
    F1 = 0x70,
    F2 = 0x71,
    F3 = 0x72,
    F4 = 0x73,
    F5 = 0x74,
    F6 = 0x75,
    F7 = 0x76,
    F8 = 0x77,
    F9 = 0x78,
    F10 = 0x79,
    F11 = 0x7A,
    F12 = 0x7B,
    F13 = 0x7C,
    F14 = 0x7D,
    F15 = 0x7E,
    F16 = 0x7F,
    F17 = 0x80,
    F18 = 0x81,
    F19 = 0x82,
    F20 = 0x83,
    F21 = 0x84,
    F22 = 0x85,
    F23 = 0x86,
    F24 = 0x87,
    NumLock = 0x90,
    LShift = 0xA0,
    RShift = 0xA1,
    LControl = 0xA2,
    RControl = 0xA3,
    LAlt = 0xA4,
    RAlt = 0xA5,
    Oem1 = 0xBA,
    Oem2 = 0xBF,
    Oem3 = 0xC0,
};

struct MenuContext {
    MENU_PAGE(PAGE_ROOT, 3175223835360298375Ui64);
    MENU_PAGE(PAGE_PERMASHOW, 10840431875876820437Ui64);

    METHOD_0(MenuPage, current_page, "core:menu_context:current_page");
    METHOD(void, title, const SmallStr& name, name, "core:menu_context:title");
    METHOD(bool, submenu, COMBINE(const MenuPage page, const SmallStr& name), COMBINE(page, name), "core:menu_context:submenu");
    METHOD(
        bool,
        toggle,
        COMBINE(const SettingsKey key, const SmallStr& name, bool* value),
        COMBINE(key, name, value),
        "core:menu_context:toggle"
    );

    bool slider(
        const SettingsKey key,
        const SmallStr& name,
        const f32 min_value,
        const f32 max_value,
        const f32 step,
        f32* value,
        const bool percent = false
    ) const {
        FN(bool, "core:menu_context:slider_f32", const void*, SettingsKey, const SmallStr&, f32, f32, f32, f32*, bool);
        return fn(this, key, name, min_value, max_value, step, value, percent);
    }

    bool slider(
        const SettingsKey key,
        const SmallStr& name,
        const i32 min_value,
        const i32 max_value,
        const i32 step,
        i32* value,
        const bool percent = false
    ) const {
        FN(bool, "core:menu_context:slider_i32", const void*, SettingsKey, const SmallStr&, i32, i32, i32, i32*, bool);
        return fn(this, key, name, min_value, max_value, step, value, percent);
    }

    METHOD(
        bool,
        select,
        COMBINE(const SettingsKey key, const SmallStr& name, const Vec<SmallStr>& options, u8* value),
        COMBINE(key, name, options, value),
        "core:menu_context:select"
    );

    bool keybind(const SettingsKey key, const SmallStr& name, VirtualKey* value, bool* state = nullptr) const {
        FN(bool, "core:menu_context:keybind", const void*, const SettingsKey, const SmallStr&, VirtualKey*, bool*);
        return fn(this, key, name, value, state);
    }

    static bool keybind_update_toggle(const SettingsKey key, const VirtualKey virtual_key, bool* value) {
        FN(bool, "core:menu_context:keybind_update_toggle", SettingsKey, VirtualKey, bool*);
        return fn(key, virtual_key, value);
    }

    static bool keybind_update_hold(const VirtualKey virtual_key, bool* value) {
        FN(bool, "core:menu_context:keybind_update_hold", VirtualKey, bool*);
        return fn(virtual_key, value);
    }

    void permashow(const SmallStr& name, const VirtualKey& key, const bool& state, const bool hold = false) const {
        FN(void, "core:menu_context:permashow", const void*, const SmallStr&, const VirtualKey&, const bool&, bool);
        return fn(this, name, key, state, hold);
    }
};

#define MENU_TOGGLE(key_name, key_value, var_name, default_value) \
    static constexpr SettingsKey key_name = key_value;            \
    bool var_name = bool(settings_init(key_name, u64(default_value)));

#define INIT_MENU_TOGGLE(key_name, var_name) var_name = bool(settings_init(key_name, u64(var_name)));

#define MENU_SLIDER_F32(key_name, key_value, var_name, default_value) \
    static constexpr SettingsKey key_name = key_value;                \
    f32 var_name = f32(settings_init(key_name, u64(default_value)));

#define INIT_MENU_SLIDER_F32(key_name, var_name) var_name = f32(settings_init(key_name, u64(var_name)));

#define MENU_SLIDER_PERCENT(key_name, key_value, var_name, default_value) MENU_SLIDER_F32(key_name, key_value, var_name, default_value)

#define INIT_MENU_SLIDER_PERCENT(key_name, var_name) INIT_MENU_SLIDER_F32(key_name, var_name)

#define MENU_SLIDER_I32(key_name, key_value, var_name, default_value) \
    static constexpr SettingsKey key_name = key_value;                \
    i32 var_name = i32(settings_init(key_name, u64(default_value)));

#define INIT_MENU_SLIDER_I32(key_name, var_name) var_name = i32(settings_init(key_name, u64(var_name)));

#define MENU_KEYBIND(key_name, key_value, var_name, default_value) \
    static constexpr SettingsKey key_name = key_value;             \
    VirtualKey var_name = VirtualKey(settings_init(key_name, u64(default_value)));

#define INIT_MENU_KEYBIND(key_name, var_name) var_name = VirtualKey(settings_init(key_name, u64(var_name)));

#define MENU_KEYBIND_STATE(key_name, key_value, var_name, default_value) MENU_TOGGLE(key_name, key_value, var_name, default_value)

#define INIT_MENU_KEYBIND_STATE(key_name, var_name) INIT_MENU_TOGGLE(key_name, var_name)

#define MENU_SELECT(key_name, key_value, var_name, default_value) \
    static constexpr SettingsKey key_name = key_value;            \
    u8 var_name = u8(settings_init(key_name, u64(default_value)));

#define INIT_MENU_SELECT(key_name, var_name) var_name = u8(settings_init(key_name, u64(var_name)));

enum class PredSpellType : u8 {
    Linear,
    Circular,
    Cone
};

enum class PredCollisionType : u8 {
    None = 0,
    Minion = 1 << 0,
    Hero = 1 << 1,
    Windwall = 1 << 2,
    Turret = 1 << 3,
    Terrain = 1 << 4,
};

inline constexpr PredCollisionType operator|(PredCollisionType a, PredCollisionType b) {
    return static_cast<PredCollisionType>(static_cast<u8>(a) | static_cast<u8>(b));
}

struct PredSpell {
    PredSpellType spell_type = PredSpellType::Linear;
    f32 cast_delay = 0.0f;
    f32 radius = 0.0f;
    f32 range = 0.0f;
    f32 speed = 0.0f;
    bool range_with_bounding_radius = false;
    bool hitbox_with_bounding_radius = false;
    PredCollisionType collisions = PredCollisionType::None;
};

enum class PredKind : u8 {
    OutOfRange,
    Collision,
    Stand,
    Dash,
    Path,
    Cast,
    Fear,
    Immobile,
    Controlled,
};

enum class PredHitChance : u8 {
    Low,
    Medium,
    High,
    VeryHigh,
    None = 255,
};

inline bool operator==(PredHitChance a, u8 b) {
    return static_cast<u8>(a) == b;
}

inline bool operator>=(PredHitChance a, u8 b) {
    return static_cast<u8>(a) >= b;
}

struct PredResult {
    PredKind kind = PredKind::OutOfRange;
    PredHitChance hit_chance = PredHitChance::None;
    Position position = Position();
    Position cast_position = Position();

    inline bool is_valid() const {
        return kind != PredKind::OutOfRange && kind != PredKind::Collision && hit_chance != PredHitChance::None;
    }
};

struct ObjectID {
    union {
        struct {
            u16 index;
            u16 next_index;
        };

        u32 id;
    };

    inline bool operator==(const ObjectID& second) const {
        return second.id == id;
    }

    inline operator u16() const {
        return index;
    }

    inline operator u32() const {
        return id;
    }

    inline bool empty() const {
        return id == 0;
    }

    inline bool valid() const {
        return id != 0;
    }
};

struct CharacterIntermediate {
    FIELD(f32, armor, "core:character_intermediate:armor");
    FIELD(f32, bonus_armor, "core:character_intermediate:bonus_armor");
    FIELD(f32, spell_block, "core:character_intermediate:spell_block");
    FIELD(f32, health_regen, "core:character_intermediate:health_regen");
    FIELD(f32, percent_armor_penetration, "core:character_intermediate:percent_armor_penetration");
    FIELD(f32, percent_bonus_armor_penetration, "core:character_intermediate:percent_bonus_armor_penetration");
    FIELD(f32, physical_lethality, "core:character_intermediate:physical_lethality");
    FIELD(f32, flat_magic_penetration, "core:character_intermediate:flat_magic_penetration");
    FIELD(f32, percent_magic_penetration, "core:character_intermediate:percent_magic_penetration");
};

struct BuffInstance {
    FIELD(BuffType, type, "core:buff_instance:type");
    FIELD(const char*, name, "core:buff_instance:name");
    FIELD(SpellHash, hash, "core:buff_instance:hash");
    FIELD(f32, start_time, "core:buff_instance:start_time");
    FIELD(f32, end_time, "core:buff_instance:end_time");
    FIELD(f32, duration, "core:buff_instance:duration");
    FIELD(f32*, stats, "core:buff_instance:stats");
    FIELD(u32, stacks, "core:buff_instance:stacks");
    FIELD(u32, count, "core:buff_instance:count");

    FINLINE bool is(SpellHash hash_) const {
        return hash() == hash_;
    }
};

struct SpellDataResource {
    FIELD(f32, cast_time, "core:spell_data_resource:cast_time");
    FIELD(const f32*, channel_duration, "core:spell_data_resource:channel_duration");
    FIELD(const f32*, cooldown_time, "core:spell_data_resource:cooldown_time");
    FIELD(bool, can_not_be_suppressed, "core:spell_data_resource:can_not_be_suppressed");
    FIELD(bool, can_cast_while_disabled, "core:spell_data_resource:can_cast_while_disabled");
    FIELD(bool, can_cast_or_queue_while_casting, "core:spell_data_resource:can_cast_or_queue_while_casting");
    FIELD(bool, cant_cancel_while_winding_up, "core:spell_data_resource:cant_cancel_while_winding_up");
    FIELD(bool, can_only_cast_while_disabled, "core:spell_data_resource:can_only_cast_while_disabled");
    FIELD(bool, cant_cancel_while_channeling, "core:spell_data_resource:cant_cancel_while_channeling");
    FIELD(bool, cant_cast_while_rooted, "core:spell_data_resource:cant_cast_while_rooted");
    FIELD(bool, channel_is_interrupted_by_disables, "core:spell_data_resource:channel_is_interrupted_by_disables");
    FIELD(bool, channel_is_interrupted_by_attacking, "core:spell_data_resource:channel_is_interrupted_by_attacking");
    FIELD(bool, apply_attack_damage, "core:spell_data_resource:apply_attack_damage");
    FIELD(bool, doesnt_break_channels, "core:spell_data_resource:doesnt_break_channels");
    FIELD(bool, use_charge_channeling, "core:spell_data_resource:use_charge_channeling");
    FIELD(bool, can_move_while_channeling, "core:spell_data_resource:can_move_while_channeling");
    FIELD(bool, is_toggle_spell, "core:spell_data_resource:is_toggle_spell");
    FIELD(const f32*, mana, "core:spell_data_resource:mana");
    FIELD(f32, spell_cooldown_or_sealed_queue_threshold, "core:spell_data_resource:spell_cooldown_or_sealed_queue_threshold");
};

struct SpellData {
    FIELD(SpellHash, spell_hash, "core:spell_data:spell_hash");
    FIELD(SpellDataResource*, resource, "core:spell_data:resource");

    METHOD(const f32*, get_data_values, const SpellHash& hash, hash, "core:spell_data:get_data_values");

    FINLINE bool is(SpellHash hash) const {
        return spell_hash() == hash;
    }
};

struct SpellInstance {
    FIELD(SpellSlot, slot, "core:spell_instance:slot");
    FIELD(SpellHash, hash, "core:spell_instance:hash");
    FIELD(const char*, name, "core:spell_instance:name");
    FIELD(bool, is_ready, "core:spell_instance:is_ready");
    FIELD(u32, level, "core:spell_instance:level");
    FIELD(f32, mana, "core:spell_instance:mana");
    FIELD(f32, range, "core:spell_instance:range");
    FIELD(f32, cool_down_expire, "core:spell_instance:cool_down_expire");
    FIELD(f32, cool_down_remaining, "core:spell_instance:cool_down_remaining");
    FIELD(SpellData*, spell_data, "core:spell_instance:spell_data");

    METHOD_0(bool, cast_self, "core:spell_instance:cast_self");
    METHOD(bool, cast_position, Position position, position, "core:spell_instance:cast_position");
    METHOD(
        bool,
        cast_direction,
        COMBINE(Position start_position, Position end_position),
        COMBINE(start_position, end_position),
        "core:spell_instance:cast_direction"
    );
    METHOD(bool, cast_charged, Position position, position, "core:spell_instance:cast_charged");
    METHOD(bool, cast_target, struct AttackableUnit* target, target, "core:spell_instance:cast_target");

    FINLINE bool is(SpellHash hash_) const {
        return hash() == hash_;
    }

    FINLINE const f32* get_data_values(const SpellHash& hash) const {
        const SpellData* data = spell_data();
        if (!data) {
            return nullptr;
        }
        return data->get_data_values(hash);
    }
};

struct SpellCastInfo {
    FIELD(SpellData*, spell_data, "core:spell_cast_info:spell_data");
    FIELD(f32, cast_time, "core:spell_cast_info:cast_time");
    FIELD(u32, caster_id, "core:spell_cast_info:caster_id");
    FIELD(u32, network_id, "core:spell_cast_info:network_id");
    FIELD(Position, start_position, "core:spell_cast_info:start_position");
    FIELD(Position, end_position, "core:spell_cast_info:end_position");
    FIELD(Position, cast_position, "core:spell_cast_info:cast_position");
    FIELD(Vec<ObjectID>, target_ids, "core:spell_cast_info:target_ids");
    FIELD(f32, cast_delay, "core:spell_cast_info:cast_delay");
    FIELD(f32, channel_duration, "core:spell_cast_info:channel_duration");
    FIELD(f32, cool_down, "core:spell_cast_info:cool_down");
    FIELD(bool, is_basic_attack, "core:spell_cast_info:is_basic_attack");
    FIELD(bool, is_special_attack, "core:spell_cast_info:is_special_attack");
    FIELD(bool, is_headshot_attack, "core:spell_cast_info:is_headshot_attack");
    FIELD(SpellSlot, spell_slot, "core:spell_cast_info:spell_slot");
    FIELD(SpellHash, spell_hash, "core:spell_cast_info:spell_hash");

    FINLINE bool is(SpellHash hash) const {
        return spell_hash() == hash;
    }

    FINLINE const f32* get_data_values(const SpellHash& hash) const {
        const SpellData* data = spell_data();
        if (!data) {
            return nullptr;
        }
        return data->get_data_values(hash);
    }
};

struct SpellInstanceClient {
    FIELD(SpellCastInfo*, info, "core:spell_instance_client:info");
    FIELD(f32, channel_start_time, "core:spell_instance_client:channel_start_time");
    FIELD(f32, channel_end_time, "core:spell_instance_client:channel_end_time");
    FIELD(SpellHash, spell_hash, "core:spell_instance_client:spell_hash");

    FINLINE bool is(SpellHash hash) const {
        return spell_hash() == hash;
    }

    FINLINE const f32* get_data_values(const SpellHash& hash) const {
        const SpellData* data = info()->spell_data();
        if (!data) {
            return nullptr;
        }
        return data->get_data_values(hash);
    }
};

struct PathController {
    FIELD(f32, movement_speed, "core:path_controller:movement_speed");
    FIELD(bool, is_moving, "core:path_controller:is_moving");
    FIELD(f32, dash_speed, "core:path_controller:dash_speed");
    FIELD(bool, is_dashing, "core:path_controller:is_dashing");
    FIELD(Position, start_position, "core:path_controller:start_position");
    FIELD(Position, end_position, "core:path_controller:end_position");
    FIELD(u32, current_waypoint_index, "core:path_controller:current_waypoint_index");
    FIELD(Vec<Position>, waypoints, "core:path_controller:waypoints");
};

struct GameObject {
    FIELD(ObjectID, id, "core:game_object:id");
    FIELD(u32, network_id, "core:game_object:network_id");
    FIELD(Team, team, "core:game_object:team");
    FIELD(SmallStr, name, "core:game_object:name");
    FIELD(Position, position, "core:game_object:position");
    FIELD(f32, bounding_radius, "core:game_object:bounding_radius");
    FIELD(bool, is_on_screen, "core:game_object:is_on_screen");
    FIELD(bool, is_in_fow, "core:game_object:is_in_fow");
    FIELD(bool, is_dead, "core:game_object:is_dead");
    FIELD(bool, is_zombie, "core:game_object:is_zombie");
    FIELD(bool, is_local_player, "core:game_object:is_local_player");
    FIELD(bool, is_ally, "core:game_object:is_ally");
    FIELD(bool, is_enemy, "core:game_object:is_enemy");
    FIELD(bool, is_hero, "core:game_object:is_hero");
    FIELD(bool, is_minion, "core:game_object:is_minion");
    FIELD(bool, is_turret, "core:game_object:is_turret");
    FIELD(bool, is_inhibitor, "core:game_object:is_inhibitor");
    FIELD(bool, is_nexus, "core:game_object:is_nexus");
    FIELD(bool, is_missile, "core:game_object:is_missile");
    FIELD(bool, is_emitter, "core:game_object:is_emitter");

    struct AIMinionClient* as_minion() const {
        if (!is_minion()) {
            return nullptr;
        }
        return (struct AIMinionClient*)(this);
    }

    struct AIHeroClient* as_hero() const {
        if (!is_hero()) {
            return nullptr;
        }
        return (struct AIHeroClient*)(this);
    }
};

struct MissileClient: GameObject {};

struct GeneralParticleEmitter: GameObject {};

struct AttackableUnit: GameObject {
    FIELD(bool, is_visible, "core:attackable_unit:is_visible");
    FIELD(bool, is_targetable, "core:attackable_unit:is_targetable");
    FIELD(bool, is_invulnerable, "core:attackable_unit:is_invulnerable");
    FIELD(f32, health, "core:attackable_unit:health");
    FIELD(f32, max_health, "core:attackable_unit:max_health");
    FIELD(f32, mana, "core:attackable_unit:mana");
    FIELD(f32, max_mana, "core:attackable_unit:max_mana");
    FIELD(f32, ammo, "core:attackable_unit:ammo");
    FIELD(f32, max_ammo, "core:attackable_unit:max_ammo");
    FIELD(f32, all_shield, "core:attackable_unit:all_shield");
    FIELD(f32, physical_shield, "core:attackable_unit:physical_shield");
    FIELD(f32, magical_shield, "core:attackable_unit:magical_shield");
    FIELD(GameObject*, gold_redirect_target, "core:attackable_unit:gold_redirect_target");
    FIELD(f32, time_of_death, "core:attackable_unit:time_of_death");

    bool validate_collisions(
        Position position,
        f32 cast_delay,
        f32 missile_speed,
        f32 missile_radius,
        PredCollisionType collisions,
        Position source_position = Position()
    ) const {
        FN(bool, "core:attackable_unit:validate_collisions", const void*, Position, f32, f32, f32, PredCollisionType, Position);
        return fn(this, position, cast_delay, missile_speed, missile_radius, collisions, source_position);
    }
};

struct Nexus: AttackableUnit {};

struct Inhibitor: AttackableUnit {};

struct AIBaseClient: AttackableUnit {
    FIELD(CharacterHash, character_hash, "core:ai_base_client:character_hash");
    FIELD(const char*, character_name, "core:ai_base_client:character_name");
    FIELD(const char*, character_display_name, "core:ai_base_client:character_display_name");
    FIELD(struct CharacterIntermediate*, character_intermediate, "core:ai_base_client:character_intermediate");
    FIELD(Position, direction, "core:ai_base_client:direction");
    FIELD(CombatType, combat_type, "core:ai_base_client:combat_type");
    FIELD(struct BuffManager*, buff_manager, "core:ai_base_client:buff_manager");
    FIELD(struct SpellBook*, spell_book, "core:ai_base_client:spell_book");
    FIELD(struct SpellInstanceClient*, active_spell, "core:ai_base_client:active_spell");
    FIELD(struct SpellCastInfo*, auto_attack_cast_info, "core:ai_base_client:auto_attack_cast_info");
    FIELD(struct PathController*, path_controller, "core:ai_base_client:path_controller");
    FIELD(f32, respawn_time, "core:ai_base_client:respawn_time");
    FIELD(f32, auto_attack_range, "core:ai_base_client:auto_attack_range"); // = character_intermediate.attack_range + bounding_radius
    FIELD(f32, base_physical_damage, "core:ai_base_client:base_physical_damage");
    FIELD(f32, bonus_physical_damage, "core:ai_base_client:bonus_physical_damage");
    FIELD(f32, physical_damage, "core:ai_base_client:physical_damage");
    FIELD(f32, magical_damage, "core:ai_base_client:magical_damage");
    FIELD(ObjectID, auto_attack_target_id, "core:ai_base_client:auto_attack_target_id");
    FIELD(f32, attack_cast_delay, "core:ai_base_client:attack_cast_delay");
    FIELD(f32, attack_delay, "core:ai_base_client:attack_delay");

    FIELD(void*, default_square_texture_view, "core:ai_base_client:default_square_texture_view");
    FIELD(void*, default_circle_texture_view, "core:ai_base_client:default_circle_texture_view");
    FIELD(void*, skinned_circle_texture_view, "core:ai_base_client:skinned_circle_texture_view");

    METHOD(bool, has_buff, SpellHash hash, hash, "core:ai_base_client:has_buff");
    METHOD(bool, has_buff_of_type, BuffType type, type, "core:ai_base_client:has_buff_of_type");
    METHOD(struct BuffInstance*, get_buff, SpellHash hash, hash, "core:ai_base_client:get_buff");
    METHOD(struct SpellInstance*, get_spell, SpellSlot slot, slot, "core:ai_base_client:get_spell");

    METHOD(f32, predicted_health, const f32 delay, delay, "core:ai_base_client:predicted_health");
    METHOD(Position, predict_path_position, const f32 delay, delay, "core:ai_base_client:predict_path_position");

    PredResult predict_cast_position(const PredSpell* spell, const Position source_position = Position()) const {
        FN(PredResult, "core:ai_base_client:predict_cast_position", const void*, const PredSpell*, Position);
        return fn(this, spell, source_position);
    }

    f32 calc_aa_damage(struct AIBaseClient* target, const bool apply_on_hit = true, const bool apply_shen = true) const {
        FN(f32, "core:ai_base_client:calc_aa_damage", const void*, struct AIBaseClient*, bool, bool);
        return fn(this, target, apply_on_hit, apply_shen);
    }

    METHOD(
        f32,
        calc_physical_damage,
        COMBINE(struct AIBaseClient* source, f32 damage),
        COMBINE(source, damage),
        "core:ai_base_client:calc_physical_damage"
    );
    METHOD(
        f32,
        calc_magical_damage,
        COMBINE(struct AIBaseClient* source, f32 damage),
        COMBINE(source, damage),
        "core:ai_base_client:calc_magical_damage"
    );

    bool is_facing(const Position& source_position, const f32 angle = 180.0f) const {
        FN(bool, "core:ai_base_client:is_facing", const void*, const Position&, f32);
        return fn(this, source_position, angle);
    }

    FINLINE bool is_facing(const AIBaseClient* source, const f32 angle = 180.0f) const {
        return is_facing(source->position(), angle);
    }

    FINLINE bool is(CharacterHash hash) const {
        return character_hash() == hash;
    }
};

struct AITurretClient: AIBaseClient {
    FIELD(u32, target_network_id, "core:ai_turret_client:target_network_id");
};

struct AIMinionClient: AIBaseClient {
    FIELD(bool, is_lane_minion, "core:ai_minion_client:is_lane_minion");
    FIELD(bool, is_siege, "core:ai_minion_client:is_siege");
    FIELD(bool, is_super, "core:ai_minion_client:is_super");
    FIELD(bool, is_clone, "core:ai_minion_client:is_clone");
    FIELD(bool, is_epic, "core:ai_minion_client:is_epic");
    FIELD(bool, is_dragon, "core:ai_minion_client:is_dragon");
    FIELD(bool, is_normal_jungle_monster, "core:ai_minion_client:is_normal_jungle_monster");
    FIELD(bool, is_small_jungle_monster, "core:ai_minion_client:is_small_jungle_monster");
    FIELD(bool, is_jungle_monster, "core:ai_minion_client:is_jungle_monster");
    FIELD(bool, is_ward, "core:ai_minion_client:is_ward");
    FIELD(bool, is_trap, "core:ai_minion_client:is_trap");
    FIELD(bool, is_pet, "core:ai_minion_client:is_pet");
    FIELD(bool, is_plant, "core:ai_minion_client:is_plant");
};

struct AIHeroClient: AIBaseClient {
    FIELD(u32, level, "core:ai_hero_client:level");
    FIELD(f32, experience, "core:ai_hero_client:experience");
    FIELD(f32, shutdown_value, "core:ai_hero_client:shutdown_value");
    FIELD(bool, is_cced, "core:ai_hero_client:is_cced");

    METHOD(bool, has_item, ItemID id, id, "core:ai_hero_client:has_item");

    f32 calc_on_hit_damage(struct AIBaseClient* target, const bool apply_shen = true) const {
        FN(f32, "core:ai_hero_client:calc_on_hit_damage", const void*, struct AIBaseClient*, bool);
        return fn(this, target, apply_shen);
    }
};

struct Module {
    struct Events {
        void (*on_update)(void* data) = nullptr;

        void (*on_create_object)(GameObject* obj) = nullptr;
        void (*on_delete_object)(GameObject* obj) = nullptr;

        void (*on_pre_spell_launch)(OnPreSpellLaunchEvent* event) = nullptr;
        void (*on_start_spell_cast)(OnStartSpellCastEvent* event) = nullptr;
        void (*on_execute_cast_frame)(OnExecuteCastFrameEvent* event) = nullptr;
        void (*on_spell_impact)(OnSpellImpactEvent* event) = nullptr;
        void (*on_stop_cast)(OnStopCastEvent* event) = nullptr;
        void (*on_buff)(OnBuffEvent* event) = nullptr;

        void (*on_play_animation)(OnPlayAnimationEvent* event) = nullptr;

        void (*on_before_attack)(OnBeforeAttackEvent* event) = nullptr;
        void (*on_after_attack)(void*) = nullptr;
        void (*on_before_move)(OnBeforeMoveEvent* event) = nullptr;
        void (*on_validate_and_cast_spell)(OnValidateAndCastSpellhEvent* event) = nullptr;

        void (*on_draw_ground)(void*) = nullptr;
        void (*on_draw_hud)(void*) = nullptr;
        void (*on_draw_menu)(MenuContext* ctx) = nullptr;
    };

    using FnEventCallback = void (*)(void*);
    METHOD(void, register_event, COMBINE(EventKind type, FnEventCallback callback), COMBINE(type, callback), "core:module:register_event");
};

static void register_module(const SmallStr name, Module::Events events) {
    FN(Module*, "core:register_module", const SmallStr);
    Module* module = fn(name);
    if (events.on_update) {
        module->register_event(EventKind::OnUpdate, reinterpret_cast<Module::FnEventCallback>(events.on_update));
    }
    if (events.on_create_object) {
        module->register_event(EventKind::OnCreateObject, reinterpret_cast<Module::FnEventCallback>(events.on_create_object));
    }
    if (events.on_delete_object) {
        module->register_event(EventKind::OnDeleteObject, reinterpret_cast<Module::FnEventCallback>(events.on_delete_object));
    }
    if (events.on_pre_spell_launch) {
        module->register_event(EventKind::OnPreSpellLaunch, reinterpret_cast<Module::FnEventCallback>(events.on_pre_spell_launch));
    }
    if (events.on_start_spell_cast) {
        module->register_event(EventKind::OnStartSpellCast, reinterpret_cast<Module::FnEventCallback>(events.on_start_spell_cast));
    }
    if (events.on_execute_cast_frame) {
        module->register_event(EventKind::OnExecuteCastFrame, reinterpret_cast<Module::FnEventCallback>(events.on_execute_cast_frame));
    }
    if (events.on_spell_impact) {
        module->register_event(EventKind::OnSpellImpact, reinterpret_cast<Module::FnEventCallback>(events.on_spell_impact));
    }
    if (events.on_stop_cast) {
        module->register_event(EventKind::OnStopCast, reinterpret_cast<Module::FnEventCallback>(events.on_stop_cast));
    }
    if (events.on_buff) {
        module->register_event(EventKind::OnBuff, reinterpret_cast<Module::FnEventCallback>(events.on_buff));
    }
    if (events.on_play_animation) {
        module->register_event(EventKind::OnPlayAnimation, reinterpret_cast<Module::FnEventCallback>(events.on_play_animation));
    }
    if (events.on_before_attack) {
        module->register_event(EventKind::OnBeforeAttack, reinterpret_cast<Module::FnEventCallback>(events.on_before_attack));
    }
    if (events.on_after_attack) {
        module->register_event(EventKind::OnAfterAttack, reinterpret_cast<Module::FnEventCallback>(events.on_after_attack));
    }
    if (events.on_before_move) {
        module->register_event(EventKind::OnBeforeMove, reinterpret_cast<Module::FnEventCallback>(events.on_before_move));
    }
    if (events.on_validate_and_cast_spell) {
        module->register_event(
            EventKind::OnValidateAndCastSpell, reinterpret_cast<Module::FnEventCallback>(events.on_validate_and_cast_spell)
        );
    }
    if (events.on_draw_ground) {
        module->register_event(EventKind::OnDrawGround, reinterpret_cast<Module::FnEventCallback>(events.on_draw_ground));
    }
    if (events.on_draw_hud) {
        module->register_event(EventKind::OnDrawHud, reinterpret_cast<Module::FnEventCallback>(events.on_draw_hud));
    }
    if (events.on_draw_menu) {
        module->register_event(EventKind::OnDrawMenu, reinterpret_cast<Module::FnEventCallback>(events.on_draw_menu));
    }
}

static void log(const char* format, ...) {
    FN(void, "core:log", const char*, va_list);
    va_list args;
    va_start(args, format);
    fn(format, args);
    va_end(args);
}

#define STR_TOGGLE(str) StrView(str).toggle()
#define STRV_TOGGLE(str) (const_cast<StrView&>(str)).toggle()
#define CONSOLE_LOG(format, ...)           \
    {                                      \
        static constexpr Str fmt = format; \
        log(STR_TOGGLE(fmt), __VA_ARGS__); \
        STR_TOGGLE(fmt);                   \
    }

STATIC_FUNCTION(f32, get_time, , , "core:get_time");
STATIC_FUNCTION(f32, get_latency, , , "core:get_latency");
STATIC_FUNCTION(Vec2, world_to_screen, Position position, position, "core:world_to_screen");
STATIC_FUNCTION(Position, get_world_cursor, , , "core:get_world_cursor");
STATIC_FUNCTION(bool, is_on_screen, const Position& position, position, "core:is_on_screen");
STATIC_FUNCTION(bool, is_in_fow, const Position& position, position, "core:is_in_fow");

STATIC_FUNCTION(AIHeroClient*, get_player, , , "core:get_player");
STATIC_FUNCTION(const Vec<AIHeroClient*>&, get_heroes, , , "core:get_heroes");
STATIC_FUNCTION(const Vec<AIHeroClient*>&, get_ally_heroes, , , "core:get_ally_heroes");
STATIC_FUNCTION(const Vec<AIHeroClient*>&, get_enemy_heroes, , , "core:get_enemy_heroes");
STATIC_FUNCTION(const Vec<AITurretClient*>&, get_turrets, , , "core:get_turrets");
STATIC_FUNCTION(const Vec<AITurretClient*>&, get_ally_turrets, , , "core:get_ally_turrets");
STATIC_FUNCTION(const Vec<AITurretClient*>&, get_enemy_turrets, , , "core:get_enemy_turrets");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_minions, , , "core:get_minions");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_neutral_minions, , , "core:get_neutral_minions");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_ally_minions, , , "core:get_ally_minions");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_minions, , , "core:get_enemy_minions");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_clones, , , "core:get_enemy_clones");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_ally_lane_minions, , , "core:get_ally_lane_minions");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_lane_minions, , , "core:get_enemy_lane_minions");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_jungle_monsters, , , "core:get_jungle_monsters");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_normal_jungle_monsters, , , "core:get_normal_jungle_monsters");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_small_jungle_monsters, , , "core:get_small_jungle_monsters");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_ally_wards, , , "core:get_ally_wards");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_wards, , , "core:get_enemy_wards");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_traps, , , "core:get_enemy_traps");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_pets, , , "core:get_enemy_pets");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_gangplank_barrels, , , "core:get_gangplank_barrels");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_senna_souls, , , "core:get_senna_souls");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_enemy_zac_anivia_egg, , , "core:get_enemy_zac_anivia_egg");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_plants, , , "core:get_plants");
STATIC_FUNCTION(const Vec<AIMinionClient*>&, get_arena_plants, , , "core:get_arena_plants");
STATIC_FUNCTION(const Vec<MissileClient*>&, get_missiles, , , "core:get_missiles");
STATIC_FUNCTION(const Vec<MissileClient*>&, get_ally_missiles, , , "core:get_ally_missiles");
STATIC_FUNCTION(const Vec<MissileClient*>&, get_enemy_missiles, , , "core:get_enemy_missiles");
STATIC_FUNCTION(const Vec<Inhibitor*>&, get_inhibitors, , , "core:get_inhibitors");
STATIC_FUNCTION(const Vec<Inhibitor*>&, get_ally_inhibitors, , , "core:get_ally_inhibitors");
STATIC_FUNCTION(const Vec<Inhibitor*>&, get_enemy_inhibitors, , , "core:get_enemy_inhibitors");
STATIC_FUNCTION(Nexus*, get_ally_nexus, , , "core:get_ally_nexus");
STATIC_FUNCTION(Nexus*, get_enemy_nexus, , , "core:get_enemy_nexus");
STATIC_FUNCTION(const Vec<GeneralParticleEmitter*>&, get_emitters, , , "core:get_emitters");
STATIC_FUNCTION(GameObject*, get_object_by_id, ObjectID id, id, "core:get_object_by_id");

STATIC_FUNCTION(ActionMode, get_action_mode, , , "core:get_action_mode");
STATIC_FUNCTION(bool, can_cast, , , "core:can_cast");
STATIC_FUNCTION(bool, can_move, , , "core:can_move");
STATIC_FUNCTION(bool, can_attack, , , "core:can_attack");
STATIC_FUNCTION(bool, is_attack_casting, , , "core:is_attack_casting");
STATIC_FUNCTION(bool, is_attack_cooldown, , , "core:is_attack_cooldown");
STATIC_FUNCTION(bool, is_spell_casting, , , "core:is_spell_casting");
STATIC_FUNCTION(bool, move, Position position, position, "core:move");
STATIC_FUNCTION(bool, attack, const AttackableUnit* target, target, "core:attack");

STATIC_FUNCTION(Position, get_nearest_passable_cell_center, Position position, position, "core:get_nearest_passable_cell_center");
STATIC_FUNCTION(CollisionFlags, get_collision_flags, Position position, position, "core:get_collision_flags");

FINLINE static bool is_wall(const Position& position) {
    return get_collision_flags(position).is_wall();
}

FINLINE static bool is_grass(const Position& position) {
    return get_collision_flags(position).is_grass();
}

FINLINE static bool is_building(const Position& position) {
    return get_collision_flags(position).is_building();
}

static bool is_under_ally_turret(const Position& position, const f32 bounding_radius = 65.0f) {
    FN(bool, "core:is_under_ally_turret", const Position&, const f32);
    return fn(position, bounding_radius);
}

static bool is_under_enemy_turret(const Position& position, const f32 bounding_radius = 65.0f) {
    FN(bool, "core:is_under_enemy_turret", const Position&, const f32);
    return fn(position, bounding_radius);
}

STATIC_FUNCTION(bool, is_auto_attacks_disabled, , , "core:is_auto_attacks_disabled");

static void disable_auto_attacks(FNV1A64 hash, f32 delay = 0.0f) { // 0.0f = infinity
    FN(void, "core:disable_auto_attacks", FNV1A64, f32);
    return fn(hash, delay);
}

STATIC_FUNCTION(void, enable_auto_attacks, FNV1A64 hash, hash, "core:enable_auto_attacks");

STATIC_FUNCTION(bool, is_move_disabled, , , "core:is_move_disabled");

static void disable_move(FNV1A64 hash, f32 delay = 0.0f) { // 0.0f = infinity
    FN(void, "core:disable_move", FNV1A64, f32);
    return fn(hash, delay);
}

STATIC_FUNCTION(void, enable_move, FNV1A64 hash, hash, "core:enable_move");

struct ResourceBar {
    f32 left;
    f32 top;
    f32 right;
    f32 bot;
    f32 value_percentage;
};

STATIC_FUNCTION(ResourceBar, get_resource_bar, AIBaseClient* object, object, "core:get_resource_bar");

static u64 settings_init(SettingsKey key, u64 default_value) {
    FN(u64, "core:settings_init", SettingsKey, u64);
    return fn(key, default_value);
}

constexpr f32 SERVER_TICK_TIME = 1.0f / 30.0f;

namespace game {
    STATIC_FUNCTION(SpellData*, get_charging_spell_data, , , "core:game:get_charging_spell_data");
    STATIC_FUNCTION(f32, get_charging_start_time, , , "core:game:get_charging_start_time");
} // namespace game

namespace ts {
    using FnIsValid = bool (*)(AIHeroClient* target, bool is_selected);

    struct TargetOptions {
        TargetMode target_mode = TargetMode::Default;
        DamageMode damage_mode = DamageMode::Adaptive;
        f32 range = 0.0f;
        FnIsValid is_valid = nullptr;
    };

    STATIC_FUNCTION(AIHeroClient*, get_target, const TargetOptions& options, options, "core:ts:get_target");
    STATIC_FUNCTION(Vec<AIHeroClient*>, get_targets, const TargetOptions& options, options, "core:ts:get_targets");

    struct PredTargetResult {
        AIHeroClient* target = nullptr;
        PredResult pred;
    };

    using FnIsValidPred = bool (*)(const PredResult&);

    static PredTargetResult get_pred_target(
        const TargetOptions& options,
        const PredSpell& spell,
        FnIsValidPred is_valid_pred = nullptr,
        Position source_position = Position()
    ) {
        const Vec<AIHeroClient*> targets = get_targets(options);
        for (AIHeroClient* target: targets) {
            const PredResult pred = target->predict_cast_position(&spell, source_position);
            if (pred.is_valid() && (!is_valid_pred || is_valid_pred(pred))) {
                return {target, pred};
            }
        }
        return {};
    }

    static Vec<PredTargetResult> get_pred_targets(
        const TargetOptions& options,
        const PredSpell& spell,
        FnIsValidPred is_valid_pred = nullptr,
        Position source_position = Position()
    ) {
        Vec<PredTargetResult> result;
        const Vec<AIHeroClient*> targets = get_targets(options);
        for (AIHeroClient* target: targets) {
            const PredResult pred = target->predict_cast_position(&spell, source_position);
            if (pred.is_valid() && (!is_valid_pred || is_valid_pred(pred))) {
                result.push({target, pred});
            }
        }
        return result;
    }
} // namespace ts

#ifdef SDK_IMPLEMENTATION

    #define FUNCTION(type, name, args, params, key)                     \
        type name(args) {                                               \
            static const auto fn = (type(*)(args))(load_function(key)); \
            return fn(params);                                          \
        }

FUNCTION(void*, operator new, size_t size, size, "core:mem:malloc");
FUNCTION(void*, operator new[], size_t size, size, "core:mem:malloc");
FUNCTION(void, operator delete, void* block, block, "core:mem:free");
FUNCTION(void, operator delete[], void* block, block, "core:mem:free");

using FnSetFunction = void (*)(FNV1A64 hash, const void* fn);
    #define EXPORT(key, fn) set_function(key, reinterpret_cast<const void*>(fn))

    #define EXPORTS(body)                                                         \
        __declspec(dllexport) void register_exports(FnSetFunction set_function) { \
            body                                                                  \
        }

    #define ENTRY(body)                                                   \
        __declspec(dllexport) void entry(FnLoadFunction _load_function) { \
            load_function = _load_function;                               \
            body                                                          \
        }

#endif
