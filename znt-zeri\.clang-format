# .clang-format
BasedOnStyle: Google
IndentWidth: 4
TabWidth: 4
UseTab: Never
AllowShortIfStatementsOnASingleLine: false
BreakBeforeBraces: Attach
ColumnLimit: 140
DerivePointerAlignment: false
PointerAlignment: Left
AlignConsecutiveAssignments: true
AlignConsecutiveDeclarations: true
AlignTrailingComments: true
AllowShortFunctionsOnASingleLine: Empty
AlwaysBreakAfterReturnType: None
SpaceAfterCStyleCast: true
IndentCaseLabels: true
SortIncludes: true
Cpp11BracedListStyle: true
IncludeBlocks: Regroup
MaxEmptyLinesToKeep: 1
NamespaceIndentation: All
ReflowComments: true
BinPackArguments: false
AlignAfterOpenBracket: DontAlign