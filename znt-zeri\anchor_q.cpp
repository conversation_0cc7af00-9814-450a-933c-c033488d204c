#include "anchor_q.h"

#include "main.h"

namespace zeri {

    // Check if player has <PERSON>eri R buff (overcharged state)
    bool is_overcharged() {
        return player->has_buff(SpellHash("ZeriR"));
    }

    // Find potential chain targets within range of a position
    Vec<AIBaseClient*> find_chain_targets(Position center, AIBaseClient* exclude_target) {
        Vec<AIBaseClient*> chain_targets;

        // Find enemy heroes within chain range
        for (const auto& enemy : get_enemy_heroes()) {
            if (enemy == exclude_target)
                continue;
            if (!is_valid_target(enemy))
                continue;

            f32 distance = center.distance(enemy->position());
            if (distance <= ANCHOR_Q_CHAIN_RANGE) {
                chain_targets.push(enemy);
            }
        }

        // Find enemy minions within chain range
        for (const auto& minion : get_minions()) {
            if (minion == exclude_target)
                continue;
            if (!is_valid_minion(minion))
                continue;
            if (minion->team() == player->team())
                continue;  // Skip allied minions

            f32 distance = center.distance(minion->position());
            if (distance <= ANCHOR_Q_CHAIN_RANGE) {
                chain_targets.push(minion);
            }
        }

        // Sort by distance (closest first) to prioritize nearest targets for chaining
        chain_targets.sort(
            [center](AIBaseClient* a, AIBaseClient* b) { return center.distance(a->position()) < center.distance(b->position()); });

        // Limit to maximum chain count
        if (chain_targets.len() > ANCHOR_Q_MAX_CHAINS) {
            chain_targets.resize(ANCHOR_Q_MAX_CHAINS);
        }

        return chain_targets;
    }

    // Calculate expected damage from anchor Q (primary + chains)
    f32 calc_anchor_q_damage(AIBaseClient* primary_target, const Vec<AIBaseClient*>& chain_targets) {
        if (!primary_target)
            return 0.0f;

        // Only calculate chain damage, not the initial Q damage
        f32 total_damage = 0.0f;

        // Chain targets take 30% AD damage
        f32 total_ad         = player->base_physical_damage() + player->bonus_physical_damage();
        f32 chain_raw_damage = ANCHOR_Q_CHAIN_DAMAGE_RATIO * total_ad;

        for (AIBaseClient* chain_target : chain_targets) {
            if (!chain_target)
                continue;
            f32 chain_damage = chain_target->calc_physical_damage(player, chain_raw_damage);
            total_damage += chain_damage;
        }

        return total_damage;
    }

    // Find the best anchor Q solution for the given spell parameters
    AnchorQSolution find_best_anchor_q(const PredSpell& q_pred_spell, f32 range) {
        AnchorQSolution best_solution;

        if (!is_overcharged()) {
            return best_solution;  // Not overcharged, no anchor Q available
        }

        f32 search_range = (range == FLT_MAX) ? q_pred_spell.range : min(range, q_pred_spell.range);

        // Evaluate enemy heroes as primary targets
        for (const auto& enemy : get_enemy_heroes()) {
            if (!is_valid_target(enemy, search_range))
                continue;

            // Get prediction for this enemy
            PredResult pred = enemy->predict_cast_position(&q_pred_spell, player->position());
            if (!pred.is_valid() || pred.hit_chance < PredHitChance::Medium)
                continue;

            // Find potential chain targets from the predicted hit position
            Vec<AIBaseClient*> chain_targets = find_chain_targets(pred.position, enemy);

            // Calculate total expected damage
            f32 total_damage = calc_anchor_q_damage(enemy, chain_targets);

            // Evaluate this solution
            i32 chain_count = static_cast<i32>(chain_targets.len());

            // Prioritize solutions with more chains, or higher damage if same chain count
            bool is_better = false;
            if (!best_solution.found) {
                is_better = true;
            } else if (chain_count > best_solution.chain_count) {
                is_better = true;
            } else if (chain_count == best_solution.chain_count && total_damage > best_solution.total_expected_damage) {
                is_better = true;
            }

            if (is_better) {
                best_solution.found                 = true;
                best_solution.primary_target        = enemy;
                best_solution.cast_position         = pred.cast_position;
                best_solution.chain_count           = chain_count;
                best_solution.total_expected_damage = total_damage;
            }
        }

        // If no good hero target found, consider minions as primary targets
        if (!best_solution.found) {
            for (const auto& minion : get_minions()) {
                if (!is_valid_minion(minion, search_range))
                    continue;
                if (minion->team() == player->team())
                    continue;  // Skip allied minions

                // Use simple position targeting for minions (they're less mobile)
                Position minion_pos = minion->position();
                f32      distance   = player->position().distance(minion_pos);
                if (distance > search_range)
                    continue;

                // Check if Q can hit this minion (collision validation)
                if (!minion->validate_collisions(minion_pos,
                        q_pred_spell.cast_delay,
                        q_pred_spell.speed,
                        q_pred_spell.radius,
                        q_pred_spell.collisions,
                        player->position())) {
                    continue;
                }

                // Find potential chain targets from minion position
                Vec<AIBaseClient*> chain_targets = find_chain_targets(minion_pos, minion);

                // Only consider minions if they have good chain potential
                if (chain_targets.len() == 0)
                    continue;

                // Calculate total expected damage
                f32 total_damage = calc_anchor_q_damage(minion, chain_targets);

                i32 chain_count = static_cast<i32>(chain_targets.len());

                // For minions, prioritize those that chain to heroes
                i32 hero_chains = 0;
                for (AIBaseClient* chain_target : chain_targets) {
                    if (chain_target->is_hero()) {
                        hero_chains++;
                    }
                }

                bool is_better = false;
                if (!best_solution.found) {
                    is_better = true;
                } else if (hero_chains > 0 && best_solution.chain_count == 0) {
                    is_better = true;  // Prefer any solution that chains to heroes
                } else if (chain_count > best_solution.chain_count) {
                    is_better = true;
                } else if (chain_count == best_solution.chain_count && total_damage > best_solution.total_expected_damage) {
                    is_better = true;
                }

                if (is_better) {
                    best_solution.found                 = true;
                    best_solution.primary_target        = minion;
                    best_solution.cast_position         = minion_pos;
                    best_solution.chain_count           = chain_count;
                    best_solution.total_expected_damage = total_damage;
                }
            }
        }

        return best_solution;
    }

    // Try to cast anchor Q if conditions are met, returns true if successful
    bool try_cast_anchor_q(const PredSpell& q_pred_spell, f32 range) {
        if (!is_overcharged()) {
            return false;  // Not overcharged, can't use anchor Q
        }

        if (!q_ready()) {
            return false;  // Q not ready
        }

        AnchorQSolution solution = find_best_anchor_q(q_pred_spell, range);

        if (!solution.found) {
            return false;  // No good anchor solution found
        }

        // Only cast if we have at least one chain target (otherwise normal Q is fine)
        if (solution.chain_count == 0) {
            return false;
        }

        // Cast the Q at the calculated position
        if (zeri_q->cast_position(solution.cast_position)) {
            return true;
        }

        return false;
    }

}  // namespace zeri