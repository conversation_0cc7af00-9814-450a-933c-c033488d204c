{"version": "2.0.0", "tasks": [{"label": "Build Plugin", "type": "shell", "command": "msbuild", "args": ["plugin.vcxproj", "/p:Configuration=Release", "/p:Platform=x64", "/p:TargetName=${workspaceFolderBasename}"], "group": {"kind": "build"}, "problemMatcher": []}, {"label": "<PERSON><PERSON> Plugin to ZNT", "type": "shell", "command": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": ["${workspaceFolder}/x64/Release/${workspaceFolderBasename}.zp", "c:\\znt"], "dependsOn": ["Build Plugin"], "problemMatcher": []}, {"label": "Build and Copy Plugin", "dependsOn": ["Build Plugin", "<PERSON><PERSON> Plugin to ZNT"], "dependsOrder": "sequence", "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}]}